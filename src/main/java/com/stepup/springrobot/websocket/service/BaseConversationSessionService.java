package com.stepup.springrobot.websocket.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.chat.*;
import com.stepup.springrobot.dto.grpc.TranscriptionResponseDTO;
import com.stepup.springrobot.model.chat.ConversationLogType;
import com.stepup.springrobot.model.chat.ConversationSource;
import com.stepup.springrobot.model.chat.RobotConversationResponseType;
import com.stepup.springrobot.model.chat.STTHandlerType;
import com.stepup.springrobot.service.AIRobotConversationService;
import com.stepup.springrobot.service.DatadogService;
import com.stepup.springrobot.service.SharedService;
import com.stepup.springrobot.util.ConcentusOpusDecoder;
import com.stepup.springrobot.websocket.service.grpc.NewGrpcAsrClientService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
public abstract class BaseConversationSessionService implements AutoCloseable {
    private final long conversationTimeoutMinutes;

    protected final WebSocketSession session;

    protected final ObjectMapper objectMapper;

    protected volatile AtomicBoolean isPausingRecording = new AtomicBoolean(false);

    protected ScheduledExecutorService scheduler;

    private ScheduledFuture<?> conversationTimeoutTask;

    protected ScheduledFuture<?> pingTask;

    private static final long PING_INTERVAL_SECONDS = 15L;

    protected final AIRobotConversationService aiRobotConversationService;

    protected WebSocketClient sttWebSocketClient;

    protected final ConcurrentLinkedQueue<byte[]> audioChunkQueue = new ConcurrentLinkedQueue<>();

    protected final ConcurrentLinkedQueue<byte[]> storedAudioChunkQueue = new ConcurrentLinkedQueue<>();

    private final String socketHost;

    protected final String userId;

    protected final String robotId;

    protected final String audioFormat;

    protected final STTHandlerType sttHandlerType;

    protected String language = null;

    protected Double silenceThreshold = null;

    protected final String stressTestRobotIds;

    protected AtomicBoolean hasReturnedChatResponse = new AtomicBoolean(false);

    protected final SharedService sharedService;

    protected final DatadogService datadogService;

    protected final String grpcAsrUriVi;

    protected final String grpcAsrUriEn;

    private NewGrpcAsrClientService newGrpcAsrClientService;

    private Sinks.Many<byte[]> audioSinkForGrpcStt;

    private Disposable grpcSttSubscription;

    private ConcentusOpusDecoder opusDecoder;

    protected BaseConversationSessionService(WebSocketSession session, ObjectMapper objectMapper,
                                             AIRobotConversationService aiRobotConversationService, long conversationTimeoutMinutes,
                                             String socketHost, String grpcAsrUriVi, String grpcAsrUriEn, String userId, String robotId, String audioFormat, STTHandlerType sttHandlerType,
                                             String stressTestRobotIds,
                                             SharedService sharedService,
                                             DatadogService datadogService) {
        this.session = session;
        this.objectMapper = objectMapper;
        this.aiRobotConversationService = aiRobotConversationService;
        this.conversationTimeoutMinutes = conversationTimeoutMinutes;
        this.socketHost = socketHost;
        this.robotId = robotId;
        this.grpcAsrUriVi = grpcAsrUriVi;
        this.grpcAsrUriEn = grpcAsrUriEn;
        this.userId = userId;
        this.audioFormat = audioFormat;
        this.sttHandlerType = sttHandlerType;
        this.stressTestRobotIds = stressTestRobotIds;
        this.sharedService = sharedService;
        this.datadogService = datadogService;

        // ConcentusOpusDecoder will be initialized lazily when needed
    }

    public void initializeSessionConfig() {
        logInfo("Initializing session with STT Handler Type: " + this.sttHandlerType);
//        if (this.sttHandlerType == STTHandlerType.GRPC_ASR) {
//            initializeGrpcSttClientLogic();
//        } else {
//            initializeWebSocketSttClientLogic();
//        }

        if (scheduler == null) {
            scheduler = Executors.newSingleThreadScheduledExecutor();
        }

        startConversationTimeout();
    }

    protected ConversationSource getConversationSource() {
        // Get conversation source from session attributes (set by ParamInterceptor)
        ConversationSource source = (ConversationSource) session.getAttributes().get("conversation_source");
        if (source != null) {
            return source;
        }

        // Fallback to determining from endpoint
        String endpoint = session.getUri() != null ? session.getUri().getPath() : null;
        return getConversationSourceFromEndpoint(endpoint);
    }

    private ConversationSource getConversationSourceFromEndpoint(String endpoint) {
        if (endpoint == null) {
            return ConversationSource.ROBOT; // default
        }

        switch (endpoint) {
            case "/ws/app/free_talk":
                return ConversationSource.APP;
            case "/ws/web/free_talk":
                return ConversationSource.WEB_MVP;
            default:
                // This covers CodeDefine.SOCKET_VER_2_ENDPOINT (/ws/free_talk) and /ws/v2/free_talk
                return ConversationSource.ROBOT;
        }
    }

    protected void logInfo(String message) {
        log.info("Client: {} - {} - {}", session.getRemoteAddress(), userId, message);
    }

    protected void logError(Exception e, String message) {
        log.error("Client: {} - {} - {}", session.getRemoteAddress(), userId, message, e);
    }

    private void initializeGrpcSttClientLogic() {
        String grpcUri;
        if (!StringUtils.isEmpty(language) && language.equalsIgnoreCase("vi")) {
            grpcUri = this.grpcAsrUriVi;
        } else {
            grpcUri = this.grpcAsrUriEn;
        }

        logInfo("(BE - STT gRPC) Language: " + (StringUtils.isEmpty(language) ? "en" : language));
        logInfo("(BE - STT gRPC) Initializing NewGrpcAsr Client Service" + grpcUri);

        this.newGrpcAsrClientService = new NewGrpcAsrClientService(grpcUri);
        this.audioSinkForGrpcStt = Sinks.many().unicast().onBackpressureBuffer();

        Flux<byte[]> audioFlux = audioSinkForGrpcStt.asFlux();

        this.grpcSttSubscription = this.newGrpcAsrClientService.streamAudio(audioFlux)
                .publishOn(Schedulers.boundedElastic())
                .subscribe(
                        (TranscriptionResponseDTO transcriptionResponse) -> {
                            try {
                                if (isPausingRecording.get() && !transcriptionResponse.isFinal()) {
                                    logInfo("gRPC ASR: Recording paused, ignoring non-final transcript: "
                                            + transcriptionResponse.getTranscript());
                                    return;
                                }
                                String rawMessageEquivalent = objectMapper.writeValueAsString(transcriptionResponse);
                                returnASRResult(transcriptionResponse.getTranscript(), rawMessageEquivalent,
                                        transcriptionResponse.isFinal(), null);
                            } catch (IOException e) {
                                logError(e, "(BE - STT gRPC) Error processing or sending ASR result from gRPC stream");
                            }
                        },
                        error -> {
                            logError((Exception) error, "(BE - STT gRPC) Error in gRPC ASR stream");
                            closeSessionDueToError("gRPC ASR Error: " + error.getMessage());
                        },
                        () -> {
                            logInfo("(BE - STT gRPC) gRPC ASR stream completed by service.");
                        });
        logInfo("(BE - STT gRPC) Subscribed to gRPC ASR stream.");
    }

    protected void initializeWebSocketSttClientLogic() {
        logInfo("(BE - STT WebSocket) Language: " + (StringUtils.isEmpty(language) ? "en" : language));
        try {
            Map<String, String> headers = new HashMap<>();
            String socketEndpoint;
            if (language != null) {
                socketEndpoint = socketHost + "?language=" + language;
            } else {
                socketEndpoint = socketHost + "?language=en";
            }

            if (silenceThreshold != null) {
                socketEndpoint += "&silence_threshold=" + silenceThreshold;
            }

            if (audioFormat.equals("opus")) {
                System.out.println("==== audioFormat: -opus");
                socketEndpoint += "&audio_format=opus";
            }

            System.out.println("==== audioFormat: " + session.getAttributes().get("audio_format"));


            Instant start = Instant.now();
            String finalSocketEndpoint = socketEndpoint;
            sttWebSocketClient = new WebSocketClient(new URI(finalSocketEndpoint), headers) {
                @Override
                public void onOpen(ServerHandshake handshakedata) {
                    logInfo("(BE - STT WebSocket) Connection established: " + finalSocketEndpoint
                            + ", time to connect: " + Duration.between(start, Instant.now()).toMillis() + "\n ==== audioFormat: " + audioFormat);
                    logInfo("log audioFormat: " + audioFormat);
                }

                @Override
                public void onMessage(String message) {
                    handleOnSocketClientMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    logInfo("(BE - STT WebSocket) Connection closed. Code: " + code + ", Reason: " + reason
                            + ", Initiated by " + remote);
                }

                @Override
                public void onError(Exception ex) {
                    logError(ex, "(BE - STT WebSocket) Error: " + finalSocketEndpoint);
                }
            };
            sttWebSocketClient.connect();
        } catch (Exception e) {
            logError(e, "(BE - STT WebSocket) Error initializing STT WebSocket");
        }
    }

    private void closeSessionDueToError(String reason) {
        try {
            if (session.isOpen()) {
                logInfo("Closing session due to error: " + reason);
                session.close(CloseStatus.SERVER_ERROR.withReason(reason));
            }
        } catch (IOException e) {
            logError(e, "Error closing session after STT stream error");
        }

        close();
    }

    protected void handleOnSocketClientMessage(String message) {
        try {
            logInfo("(BE - STT WebSocket) Received STT response: " + message);
            if (isPausingRecording.get()) {
                logInfo("Recording paused, not processing WebSocket STT response");
                return;
            }

            String transcript;
            boolean isEndOfSpeech;
            SpeechToTextDTO speechToTextDTO = objectMapper.readValue(message, new TypeReference<>() {
            });
            List<SpeechToTextSegmentDTO> segments = speechToTextDTO.getSegments();
            transcript = segments.get(segments.size() - 1).getText();
            isEndOfSpeech = speechToTextDTO.isFinal();

            returnASRResult(transcript, message, isEndOfSpeech, null);
        } catch (Exception e) {
            logError(e, "(BE - STT WebSocket) Error processing STT response" + e.getMessage());
        }
    }

    protected void returnASRResult(String transcript, String message, boolean isEndOfSpeech, File audioFile)
            throws IOException {
        if (StringUtils.isEmpty(transcript)) {
            transcript = "uhm...";
        }

        RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.ASR)
                .data(RobotASRMessageDTO.builder()
                        .transcript(transcript)
                        .isStop(isEndOfSpeech)
                        .build())
                .build();

        String asrResponse = objectMapper.writeValueAsString(audioMessage);
        if (session.isOpen()) {
            session.sendMessage(new TextMessage(asrResponse));
        }

        if (isEndOfSpeech && !StringUtils.isEmpty(transcript)) {
            isPausingRecording.set(true);

            if (this.sttHandlerType != STTHandlerType.GRPC_ASR && sttWebSocketClient != null) {
                sttWebSocketClient.close();
                sttWebSocketClient = null;
            }

            if (this.sttHandlerType == STTHandlerType.GRPC_ASR) {
                if (audioSinkForGrpcStt != null) {
                    newGrpcAsrClientService.close(); // Close the gRPC client if sink is null
                    newGrpcAsrClientService = null; // Reset the client to allow re-initialization
                }
            } else {
                audioChunkQueue.clear();
            }

            Long botId = getBotIdParam();
            hasReturnedChatResponse.set(false);
            getBotResponse(transcript, message, botId, audioFile);
        }
    }

    protected Long getBotIdParam() {
        Long botId = null;
        Object botIdAttr = session.getAttributes().get("bot_id");
        if (botIdAttr != null) {
            try {
                botId = Long.parseLong(String.valueOf(botIdAttr));
            } catch (NumberFormatException e) {
                logError(e, "Failed to parse bot_id: " + botIdAttr);
            }
        }
        return botId;
    }

    protected boolean getIsFromWebMvp() {
        Object isWebMvpAttr = session.getAttributes().get("is_web_mvp");
        return isWebMvpAttr != null && Boolean.parseBoolean(String.valueOf(isWebMvpAttr));
    }

    protected void startConversationTimeout() {
        logInfo("Conversation timer started. Will end in " + conversationTimeoutMinutes + " minutes.");
        conversationTimeoutTask = scheduler.schedule(() -> {
            logInfo("Conversation timeout reached. Ending the conversation.");
            stopConversation();
        }, conversationTimeoutMinutes, TimeUnit.MINUTES);
    }

    protected void stopConversation() {
        try {
            RobotResponseMessageDTO responseMessageDTO = RobotResponseMessageDTO.builder()
                    .text(CodeDefine.CONVERSATION_FINISH_SENTENCE)
                    .audio(CodeDefine.CONVERSATION_FINISH_AUDIO)
                    .build();
            RobotConversationMessageDTO audioMessage = RobotConversationMessageDTO.builder()
                    .type(RobotConversationResponseType.CHAT_RESPONSE)
                    .data(RobotChatResponseDTO.builder()
                            .messages(List.of(responseMessageDTO))
                            .isEndConversation(true)
                            .build())
                    .build();
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(objectMapper.writeValueAsString(audioMessage)));
                session.close(CloseStatus.NORMAL.withReason("Conversation time limit reached."));
            }
        } catch (IOException e) {
            logError(e, "Error sending timeout message or closing WebSocket session");
        } finally {
            isPausingRecording.set(true);
            if (conversationTimeoutTask != null && !conversationTimeoutTask.isDone()) {
                conversationTimeoutTask.cancel(true);
                logInfo("Conversation timeout task cancelled.");
            }
            close();
        }
    }

    public void handleAudioData(ByteBuffer audioData) {
        if (isPausingRecording.get()) {
            return;
        }

        boolean isConvert = getParamIsConvert(session);
        byte[] newData = isConvert ? convertAudioFormat(audioData.array()) : audioData.array();

        // Always store for potential saving
        storedAudioChunkQueue.offer(newData.clone());

        if (this.sttHandlerType == STTHandlerType.GRPC_ASR) {
            if (newGrpcAsrClientService == null) { // Ensure gRPC client was initialized
                initializeGrpcSttClientLogic(); // Attempt to initialize if not already
                if (newGrpcAsrClientService == null) { // Still null after attempt
                    logError(new IllegalStateException("gRPC client failed to initialize."),
                            "Cannot handle audio data for GRPC_ASR.");
                    return;
                }
            }
            if (audioSinkForGrpcStt != null) {
                Sinks.EmitResult result = audioSinkForGrpcStt.tryEmitNext(newData.clone());
                logInfo("(BE - STT) Sent accumulated audio chunk to GRPC server: " + newData.length + " bytes");
                if (result.isFailure()) {
                    logInfo("(BE - STT gRPC) Failed to emit audio chunk to gRPC sink: " + result);
                }
            } else {
                logError(new IllegalStateException("audioSinkForGrpcStt is null for GRPC_ASR type."),
                        "Cannot send audio to gRPC.");
                newGrpcAsrClientService.close(); // Close the gRPC client if sink is null
                newGrpcAsrClientService = null; // Reset the client to allow re-initialization
            }
        } else { // For WebSocket-based STT
            if (sttWebSocketClient == null) {
                logInfo("(BE - STT WebSocket) WebSocket STT client not initialized in handleAudioData. Initializing.");
                initializeWebSocketSttClientLogic(); // Corrected method name
                if (sttWebSocketClient == null) { // Still null after attempt
                    logError(new IllegalStateException("WebSocket STT client failed to initialize."),
                            "Cannot handle audio data for WebSocket STT.");
                    return;
                }
            }

            audioChunkQueue.offer(newData.clone()); // Use audioChunkQueue for WebSocket STT
            processQueuedData(); // Process for WebSocket STT
        }
    }

    public void handleTextMessage(String message) {
        try {
            // First try to parse as ASRTextMessageReqDTO
            try {
                ASRTextMessageReqDTO asrTextMessageReqDTO = objectMapper.readValue(message, ASRTextMessageReqDTO.class);
                if (asrTextMessageReqDTO != null && Objects.equals(asrTextMessageReqDTO.getType(), "ASR")
                        && asrTextMessageReqDTO.getText() != null && asrTextMessageReqDTO.getAudioUrl() != null) {
                    log.info("Received ASR text message: {} with audio URL: {}", asrTextMessageReqDTO.getText(),
                            asrTextMessageReqDTO.getAudioUrl());

                    // Download the audio file from the URL
                    String sessionId = session.getId();
                    String fileName = sessionId + "_" + System.currentTimeMillis() + ".wav";
                    File audioFile = downloadAudioFile(asrTextMessageReqDTO.getAudioUrl(), fileName);
                    returnASRResult(asrTextMessageReqDTO.getText(), message, true, audioFile);
                    return;
                }
            } catch (Exception e) {
                log.error("Error processing ASR text message: {}", e.getMessage(), e);
                // Not an ASRTextMessageReqDTO, continue to try other formats
            }

            // Try to parse as ChatTextMessageReqDTO
            ChatTextMessageReqDTO chatTextMessageReqDTO = objectMapper.readValue(message, new TypeReference<>() {
            });
            if (chatTextMessageReqDTO != null && !StringUtils.isEmpty(chatTextMessageReqDTO.getType())) {
                String type = chatTextMessageReqDTO.getType();
                if (type.equalsIgnoreCase("SKIP")) {
                    returnASRResult("[NEXT]", message, true, null);
                } else if (type.equalsIgnoreCase("BUTTON_LEFT")
                        || type.equalsIgnoreCase("BUTTON_CENTER")
                        || type.equalsIgnoreCase("BUTTON_RIGHT")
                        || type.equalsIgnoreCase("SILENCE")) {
                    returnASRResult(type, message, true, null);
                }
            }
        } catch (Exception e) {
            log.error("Client: {} - {} - {}", session.getRemoteAddress(), userId, message, e);
        }
    }

    protected abstract byte[] convertAudioFormat(byte[] audioData);

    protected void processQueuedData() {
        if (sttWebSocketClient == null || !sttWebSocketClient.isOpen()) {
            return;
        }

        // Safe to poll while new data is being added
        byte[] chunk = audioChunkQueue.poll();
        if (chunk != null) {
            sttWebSocketClient.send(chunk);
            logInfo("(BE - STT) Sent accumulated audio chunk to STT server: " + chunk.length + " bytes");
        }
    }

    public File saveAudio(String sessionId) {
        // Copy the audio data from storedAudioChunkQueue to binaryDataBuffer
        List<byte[]> binaryDataBuffer = new ArrayList<>(storedAudioChunkQueue);

        // Combine all the binary data into one byte array
        byte[] audioData = combineBinaryData(binaryDataBuffer);

        // Convert the byte array to an audio file
        File file = convertToWavFile(audioData, sessionId + "_" + System.currentTimeMillis() + ".wav");

        // Clear the buffer
        binaryDataBuffer.clear();
        storedAudioChunkQueue.clear();

        return file;
    }

    protected byte[] combineBinaryData(List<byte[]> binaryDataBuffer) {
        if (audioFormat.equals("opus")) {
            return handleOpusDecoding(binaryDataBuffer);
        }

        int totalLength = binaryDataBuffer.stream().mapToInt(data -> data.length).sum();
        byte[] combinedData = new byte[totalLength];
        int offset = 0;
        for (byte[] data : binaryDataBuffer) {
            System.arraycopy(data, 0, combinedData, offset, data.length);
            offset += data.length;
        }

        return combinedData;
    }

    /**
     * Initialize ConcentusOpusDecoder lazily when needed
     */
    private void initializeOpusDecoderIfNeeded() {
        if (opusDecoder == null) {
            try {
                opusDecoder = new ConcentusOpusDecoder();
                log.info("Concentus Opus decoder initialized lazily for session: {} - Pure Java, no native library required!", session.getId());
            } catch (Exception e) {
                log.error("Failed to initialize Concentus Opus decoder for session: {}", session.getId(), e);
                opusDecoder = null;
            }
        }
    }

    /**
     * Handle Opus decoding for audio data
     *
     * @param binaryDataBuffer List of Opus-encoded audio chunks
     * @return Combined PCM audio data
     */
    private byte[] handleOpusDecoding(List<byte[]> binaryDataBuffer) {
        // Initialize OpusDecoder lazily
        initializeOpusDecoderIfNeeded();

        if (opusDecoder == null) {
            log.error("Concentus Opus decoder initialization failed. Cannot decode audio data.");
            return new byte[0];
        }

        if (binaryDataBuffer == null || binaryDataBuffer.isEmpty()) {
            log.warn("Empty binary data buffer provided for Opus decoding");
            return new byte[0];
        }

        try {
            List<byte[]> decodedChunks = new ArrayList<>();
            int totalDecodedLength = 0;

            // Decode each Opus chunk
            for (byte[] opusChunk : binaryDataBuffer) {
                if (opusChunk != null && opusChunk.length > 0) {
                    byte[] decodedPcm = opusDecoder.decode(opusChunk);
                    if (decodedPcm.length > 0) {
                        decodedChunks.add(decodedPcm);
                        totalDecodedLength += decodedPcm.length;
                    }
                }
            }

            // Combine all decoded PCM chunks
            if (decodedChunks.isEmpty()) {
                log.warn("No valid decoded audio chunks from Opus data");
                return new byte[0];
            }

            byte[] combinedPcmData = new byte[totalDecodedLength];
            int offset = 0;
            for (byte[] decodedChunk : decodedChunks) {
                System.arraycopy(decodedChunk, 0, combinedPcmData, offset, decodedChunk.length);
                offset += decodedChunk.length;
            }

            log.debug("Successfully decoded {} Opus chunks to {} bytes of PCM data",
                    binaryDataBuffer.size(), combinedPcmData.length);

            return combinedPcmData;

        } catch (Exception e) {
            log.error("Error during Opus decoding process", e);
            return new byte[0];
        }
    }

    protected abstract File convertToWavFile(byte[] audioData, String fileName);

    /**
     * Downloads an audio file from a URL and saves it locally
     *
     * @param audioUrl The URL of the audio file
     * @param fileName The name to save the file as
     * @return The downloaded file, or null if download failed
     */
    protected File downloadAudioFile(String audioUrl, String fileName) {
        try {
            URL url = new URL(audioUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("Failed to download audio file from {}, response code: {}", audioUrl, responseCode);
                return null;
            }

            // Create the file
            File outputFile = new File(fileName);

            // Download the file content
            try (InputStream inputStream = connection.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(outputFile)) {

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            log.info("Successfully downloaded audio file from {} to {}", audioUrl, fileName);
            return outputFile;
        } catch (Exception e) {
            log.error("Error downloading audio file from {}: {}", audioUrl, e.getMessage(), e);
            return null;
        }
    }

    // This method is now removed as its functionality is merged into getBotResponse

    @Override
    public void close() {
        if (pingTask != null && !pingTask.isDone()) {
            pingTask.cancel(true);
            pingTask = null;
        }
        if (sttWebSocketClient != null) {
            sttWebSocketClient.close();
            sttWebSocketClient = null;
        }

        if (scheduler != null) {
            scheduler.shutdown();
            scheduler = null;
        }

        if (newGrpcAsrClientService != null) {
            grpcSttSubscription.dispose(); // Dispose of the subscription to stop receiving messages
            newGrpcAsrClientService.close(); // Close the gRPC client if sink is null
            newGrpcAsrClientService = null; // Reset the client to allow re-initialization
        }

        // Clear queues
        audioChunkQueue.clear();
        storedAudioChunkQueue.clear();

        // Cleanup Concentus Opus decoder
        if (opusDecoder != null) {
            opusDecoder.destroy();
            opusDecoder = null;
        }
    }

    /**
     * Handle bot response after receiving speech-to-text transcript
     *
     * @param transcript           The transcript text
     * @param speechToTextResponse The original speech-to-text response
     * @param botId                The bot ID
     * @param providedFile         Optional pre-existing audio file (if null, will
     *                             save audio from the queue)
     */
    protected void getBotResponse(String transcript, String speechToTextResponse, Long botId, File providedFile) {
        Instant start = Instant.now();
        StringBuilder logMessage = new StringBuilder();
        String sessionId = session.getId();

        // Use the provided file or save audio from the queue
        File file = providedFile != null ? providedFile : saveAudio(sessionId);

        // Save user answer with the file name
        if (file != null) {
            aiRobotConversationService.saveUserAnswer(file.getName(), sessionId, transcript, speechToTextResponse, language);
        }

        // Send stalling message while processing
        CompletableFuture.runAsync(() -> handleSendStalling(logMessage, transcript));

        // Get bot response
        RobotConversationMessageDTO audioMessage = handleGetBotResponse(transcript, file, sessionId,
                speechToTextResponse, botId, logMessage);

        // Send response back to client
        if (session.isOpen() && audioMessage != null) {
            try {
                String chatResponse = objectMapper.writeValueAsString(audioMessage);
                hasReturnedChatResponse.set(true);
                session.sendMessage(new TextMessage(chatResponse)); // Send the bot response back to the user
                long duration = Duration.between(start, Instant.now()).toMillis();
                sharedService.saveConversationLog(audioMessage.getConversationId(), ConversationLogType.SERVER_RESPONSE,
                        chatResponse, duration, audioMessage.getIsSessionCompleted());
                logInfo("(BE - Robot) Send bot response - conversation_id : " + audioMessage.getConversationId() + ": "
                        + chatResponse);
                String responseTime = "ROBOT - res time: " + duration + "ms";
                logMessage.insert(0, responseTime);
                log.error(logMessage.toString());
                isPausingRecording.set(false);
            } catch (IOException e) {
                logError(e, "Failed to send bot response");
            }
        }
    }

    protected void handleSendStalling(StringBuilder logMessage, String userAnswer) {
        Instant startStalling = Instant.now();
        List<RobotResponseMessageDTO> stallingMsgs = aiRobotConversationService.getStallingMessage(session.getId(),
                userAnswer);
        RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_STALLING)
                .data(stallingMsgs)
                .build();
        try {
            String chatResponse = objectMapper.writeValueAsString(stallingMsg);
            session.sendMessage(new TextMessage(chatResponse)); // Send the bot response back to the user
            logMessage.append(", stalling: ").append(Duration.between(startStalling, Instant.now()).toMillis())
                    .append("ms");
            logInfo("(BE - Robot) Send stalling bot response: " + chatResponse);
        } catch (IOException e) {
            logError(e, "Failed to send bot response");
        }
    }

    protected RobotConversationMessageDTO handleGetBotResponse(String transcript, File file, String sessionId,
                                                               String speechToTextResponse, Long botId, StringBuilder logMessage) {
        RobotConversationMsgResDTO robotConversationMsgResDTO = aiRobotConversationService.getRobotResponse(userId, robotId,
                transcript, file, sessionId, speechToTextResponse, session.getRemoteAddress().toString(), botId,
                logMessage, STTHandlerType.ASR, false, null);
        List<AIRobotConversationResDTO> resDTOS = robotConversationMsgResDTO.getMessages();

        return RobotConversationMessageDTO.builder()
                .type(RobotConversationResponseType.CHAT_RESPONSE)
                .data(robotConversationMsgResDTO.getResponseMessages())
                .conversationId(resDTOS.get(0).getConversationId())
                .build();
    }

    protected void startPingTask() {
        if (scheduler == null) {
            scheduler = Executors.newSingleThreadScheduledExecutor();
        }

        pingTask = scheduler.scheduleAtFixedRate(() -> {
            RobotConversationMessageDTO stallingMsg = RobotConversationMessageDTO.builder()
                    .type(RobotConversationResponseType.PING)
                    .socketSessionId(session.getId())
                    .build();
            try {
                String chatResponse = objectMapper.writeValueAsString(stallingMsg);
                session.sendMessage(new TextMessage(chatResponse)); // Send the bot response back to the user
                logInfo("(BE - Robot) Send ping message: " + chatResponse);
            } catch (IOException e) {
                logError(e, "Failed to send bot response");
            }
        }, 0, PING_INTERVAL_SECONDS, TimeUnit.SECONDS);
    }

    protected boolean getParamIsConvert(WebSocketSession session) {
        Object attribute = session.getAttributes().get("is_convert");
        if (attribute == null) {
            return false;
        }

        return Boolean.parseBoolean(attribute.toString());
    }
}
