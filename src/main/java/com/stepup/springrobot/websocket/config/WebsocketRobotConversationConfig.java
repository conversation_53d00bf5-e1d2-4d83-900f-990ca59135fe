package com.stepup.springrobot.websocket.config;

import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.websocket.handler.*;
import com.stepup.springrobot.websocket.service.HandlerRoutingService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

@Configuration
@EnableWebSocket
public class WebsocketRobotConversationConfig implements WebSocketConfigurer {
    private final RobotSocketConversationHandler robotSocketConversationHandler;
    private final GoogleSocketConversationHandler googleSocketConversationHandler;
    private final HandlerRoutingService handlerRoutingService;
    private final WebSocketUtils webSocketUtils;

    public WebsocketRobotConversationConfig(RobotSocketConversationHandler robotSocketConversationHandler,
            GoogleSocketConversationHandler googleSocketConversationHandler,
            HandlerRoutingService handlerRoutingService,
            WebSocketUtils webSocketUtils) {
        this.robotSocketConversationHandler = robotSocketConversationHandler;
        this.googleSocketConversationHandler = googleSocketConversationHandler;
        this.handlerRoutingService = handlerRoutingService;
        this.webSocketUtils = webSocketUtils;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new DynamicRoutingHandler(
                robotSocketConversationHandler,
                googleSocketConversationHandler,
                handlerRoutingService,
                webSocketUtils), CodeDefine.SOCKET_VER_2_ENDPOINT)
                .addInterceptors(new ParamInterceptor())
                .setAllowedOrigins("*");

        registry.addHandler(new DynamicRoutingHandler(
                robotSocketConversationHandler,
                googleSocketConversationHandler,
                handlerRoutingService,
                webSocketUtils), "/ws/v2/free_talk")
                .addInterceptors(new ParamInterceptor())
                .setAllowedOrigins("*");

        registry.addHandler(new DynamicRoutingHandler(
                        robotSocketConversationHandler,
                        googleSocketConversationHandler,
                        handlerRoutingService,
                        webSocketUtils), "/ws/app/free_talk")
                .addInterceptors(new ParamInterceptor())
                .setAllowedOrigins("*");

        registry.addHandler(new DynamicRoutingHandler(
                        robotSocketConversationHandler,
                        googleSocketConversationHandler,
                        handlerRoutingService,
                        webSocketUtils), "/ws/web/free_talk")
                .addInterceptors(new ParamInterceptor())
                .setAllowedOrigins("*");
    }

    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxBinaryMessageBufferSize(64 * 1024); // 64KB
        container.setMaxSessionIdleTimeout(3600000L); // 1 hour

        return container;
    }

}
