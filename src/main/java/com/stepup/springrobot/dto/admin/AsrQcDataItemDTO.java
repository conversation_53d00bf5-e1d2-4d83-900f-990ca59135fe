package com.stepup.springrobot.dto.admin;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsrQcDataItemDTO {
    @JsonProperty("message_id")
    private Long messageId;

    @JsonProperty("conversation_id")
    private Long conversationId;

    @JsonProperty("content")
    private String content;

    @JsonProperty("audio")
    private String audio;

    @JsonProperty("is_asr_correct")
    private Boolean isAsrCorrect;

    @JsonProperty("asr_corrected_content")
    private String asrCorrectedContent;

    @JsonProperty("is_intent_affected")
    private Boolean isIntentAffected;

    @JsonProperty("intent_corrected_content")
    private String intentCorrectedContent;
}


