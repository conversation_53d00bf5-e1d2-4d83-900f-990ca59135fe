package com.stepup.springrobot.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.alarm.AlarmScheduleDeleteReqDTO;
import com.stepup.springrobot.dto.alarm.AlarmScheduleReqDTO;
import com.stepup.springrobot.service.AlarmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@CrossOrigin("*")
@RequestMapping("robot/api/v1/alarm")
@Tag(name = "Alarm", description = "Alarm management APIs")
public class AlarmController extends BaseController {
    @Autowired
    private AlarmService alarmService;

    @Operation(summary = "Get all alarm schedules for current user", security = @SecurityRequirement(name = "Bearer Authentication"))
    @GetMapping("/schedules")
    public ResponseEntity<?> getUserAlarmSchedules(HttpServletRequest request) {
        return success(alarmService.getAllSchedules(request));
    }

    @Operation(summary = "Create a new alarm schedule", security = @SecurityRequirement(name = "Bearer Authentication"))
    @PostMapping("/schedules")
    public ResponseEntity<?> createAlarmSchedule(@Valid @RequestBody AlarmScheduleReqDTO alarmScheduleReqDTO,
                                                 HttpServletRequest request) throws JsonProcessingException {
        DataResponseDTO<?> dataResponseDTO = alarmService.createAlarmSchedule(alarmScheduleReqDTO, request);
        return success(dataResponseDTO, HttpStatus.valueOf(dataResponseDTO.getStatus()));
    }

    @Operation(summary = "Update an existing alarm schedule", security = @SecurityRequirement(name = "Bearer Authentication"))
    @PutMapping("/schedules/{id}")
    public ResponseEntity<?> updateAlarmSchedule(@PathVariable Long id, @Valid @RequestBody AlarmScheduleReqDTO alarmScheduleReqDTO, HttpServletRequest request) throws JsonProcessingException {
        alarmScheduleReqDTO.setId(id); // Ensure ID is set correctly
        DataResponseDTO<?> dataResponseDTO = alarmService.updateAlarmSchedule(id, alarmScheduleReqDTO, request);
        return success(dataResponseDTO, HttpStatus.valueOf(dataResponseDTO.getStatus()));
    }

    @Operation(summary = "Delete an alarm schedule", security = @SecurityRequirement(name = "Bearer Authentication"))
    @DeleteMapping("/schedules")
    public ResponseEntity<?> deleteAlarmSchedule(@Valid @RequestBody AlarmScheduleDeleteReqDTO reqDTO, HttpServletRequest request) {
        return success(alarmService.deleteAlarmSchedule(reqDTO, request));
    }

    @Operation(summary = "Toggle alarm schedule active status", security = @SecurityRequirement(name = "Bearer Authentication"))
    @PutMapping("/schedules/{id}/toggle")
    public ResponseEntity<?> toggleAlarmScheduleStatus(@PathVariable Long id, HttpServletRequest request) {
        return success(alarmService.toggleAlarmScheduleStatus(id, request));
    }

    @Operation(summary = "Get all alarm activities", security = @SecurityRequirement(name = "Bearer Authentication"))
    @GetMapping("/activities")
    public ResponseEntity<?> getAllActivities(HttpServletRequest request) {
        return success(alarmService.getAllActivities(request));
    }

    @GetMapping("/check_alarm_manual")
    public ResponseEntity<?> checkAndCacheAlarmSchedules(HttpServletRequest request) {
        alarmService.checkAndCacheAlarmSchedules();
        return success();
    }
}
