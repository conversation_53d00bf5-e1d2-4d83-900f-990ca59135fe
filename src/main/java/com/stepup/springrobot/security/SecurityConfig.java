package com.stepup.springrobot.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.stepup.springrobot.repository.auth.UserRepository;
import com.stepup.springrobot.service.auth.UserDetailsServiceImpl;
import com.stepup.springrobot.service.auth.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
    @Value("${swagger.auth.username}")
    private String swaggerUsername;

    @Value("${swagger.auth.password}")
    private String swaggerPassword;

    @Autowired
    private JwtAuthFilter jwtAuthFilter;

    @Autowired
    private AuthenticationConfiguration authenticationConfiguration;

    @Autowired
    private JwtService jwtService;

    @Autowired
    private RefreshTokenService refreshTokenService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Bean
    public UserDetailsService userDetailsService() {
        return new UserDetailsServiceImpl();
    }

    @Bean
    public InMemoryUserDetailsManager swaggerUserDetailsService() {
        UserDetails swaggerUser = User.withUsername(swaggerUsername)
                .password(passwordEncoder.encode(swaggerPassword))
                .roles("SWAGGER_ADMIN")
                .build();
        return new InMemoryUserDetailsManager(swaggerUser);
    }

    @Bean
    public CorsConfigurationSource authCorsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*")); // Allow all origins
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*")); // Allow all headers
        configuration.setAllowCredentials(true); // Allow credentials
        configuration.setMaxAge(3600L); // Cache preflight response for 1 hour

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // Apply CORS cho các auth endpoints (login, logout, refresh-token)
        source.registerCorsConfiguration("/robot-user/api/v1/auth/**", configuration);
        // Apply CORS cho các device endpoints
        source.registerCorsConfiguration("/robot/api/v1/device/**", configuration);
        return source;
    }

    @Bean
    @Order(1)
    public SecurityFilterChain swaggerSecurityFilterChain(HttpSecurity http) throws Exception {
        return http
                .csrf(AbstractHttpConfigurer::disable) // Disable CSRF completely
                .requestMatchers(
                        matchers -> matchers.antMatchers("/v3/api-docs/**", "/swagger-ui/**", "/swagger-ui.html"))
                .authorizeHttpRequests(auth -> auth.anyRequest().permitAll())
                .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .build();
    }

    @Bean
    @Order(2)
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        return http.csrf().disable()
                .cors(cors -> cors.configurationSource(authCorsConfigurationSource())) // Enable CORS cho auth APIs (login, logout, refresh-token)
                .authorizeRequests()
                .antMatchers("/robot-user/api/v1/auth/login", "/robot-user/api/v1/auth/logout", "/robot-user/api/v1/auth/refresh-token").permitAll()
                .antMatchers("/robot-user/api/v1/user/pre-sign-up",
                        "/robot-user/api/v1/user/send-otp-sms",
                        "/robot-user/api/v1/user/verify-otp-sms",
                        "/robot-user/api/v1/user/search",
                        "/robot-user/api/v1/user/update-password-with-otp",
                        "/robot-user/api/v1/user/logout").permitAll()
                .antMatchers("/robot/api/v1/learn/**").permitAll()
                .antMatchers("/robot/api/v1/llm/**").permitAll()
                .antMatchers("/robot/api/v1/open-api/**").permitAll()
                .antMatchers("/api/v1/robot_conversation/**").permitAll()
                .antMatchers("/robot/api/v1/mqtt/**").permitAll()
                .antMatchers("/robot/api/v1/admin/**").permitAll()
                .antMatchers("/robot/api/v1/robot-open/**").permitAll()
                .antMatchers(HttpMethod.POST, "/robot-user/api/v1/user").permitAll()
                .antMatchers("/robot/api/v1/device/**").permitAll()
                .antMatchers("/robot/api/v1/report_info/**").permitAll()
                .antMatchers("/web/api/v1/**").permitAll()
                .antMatchers("/api/v1/webhook/ladipage/**").permitAll()
                .antMatchers("/robot/api/v1/study/export/**").permitAll()
                .and()
                .authorizeRequests().antMatchers("/robot/api/v1/**")
                .authenticated()
                .and()
                .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authenticationProvider(authenticationProvider())
                .addFilter(new JwtUsernameAndPasswordAuthenticationFilter(authenticationManager(), objectMapper, jwtService, refreshTokenService, userService, userRepository))
                .addFilterBefore(jwtAuthFilter, UsernamePasswordAuthenticationFilter.class)
                .build();
    }

    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(userDetailsService());
        authenticationProvider.setPasswordEncoder(passwordEncoder);
        return authenticationProvider;
    }

    @Bean
    protected AuthenticationManager authenticationManager() throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }
}
