package com.stepup.springrobot.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.ielts.ChatResDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class DatadogService {
    @Value("${datadog_endpoint}")
    private String endpoint;

    @Value("${datadog_api_key}")
    private String apiKey;

    @Value("${datadog_application_key}")
    private String applicationKey;

    @Value("${datadog_host}")
    private String hostName;

    @Value("${datadog_service}")
    private String serviceName;

    @Autowired
    private ObjectMapper objectMapper;

    @Async
    public void sendLogToDatadog(int status, String message) {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(20, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .build();

            ObjectNode data = JsonNodeFactory.instance.objectNode();
            data.put("host", hostName);
            data.put("service", serviceName);
            data.put("message", message);
            data.put("status", status);

            Request request = new Request.Builder()
                    .url(endpoint)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("DD-API-KEY", apiKey)
                    .addHeader("DD-APPLICATION-KEY", applicationKey)
                    .post(RequestBody.create(okhttp3.MediaType.parse("application/json"), objectMapper.writeValueAsString(data)))
                    .build();

            Response response = client.newCall(request).execute();
            DataResponseDTO<ChatResDTO> chatResDTO = objectMapper.readValue(response.body().string(), new TypeReference<>() {
            });
            log.info("Sent log to datadog successfully: {}", objectMapper.writeValueAsString(chatResDTO));
        } catch (Exception e) {
           log.error("Sent log to datadog error: {}", e.getMessage());
        }
    }
}
