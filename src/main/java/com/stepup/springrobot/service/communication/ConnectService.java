package com.stepup.springrobot.service.communication;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.stepup.springrobot.common.CodeDefine;
import com.stepup.springrobot.dto.DataResponseDTO;
import com.stepup.springrobot.dto.auth.AccountInfoDTO;
import com.stepup.springrobot.dto.communication.*;
import com.stepup.springrobot.dto.mqtt.MqttMessageDTO;
import com.stepup.springrobot.dto.mqtt.PublishMqttMessageReqDTO;
import com.stepup.springrobot.exception.business.content.ContentNotFoundException;
import com.stepup.springrobot.exception.business.request.CommunicationTimeoutException;
import com.stepup.springrobot.model.alarm.AlarmActivityType;
import com.stepup.springrobot.model.alarm.AlarmScheduleExecution;
import com.stepup.springrobot.model.communication.CommunicationRequest;
import com.stepup.springrobot.model.communication.CommunicationRequestStatusType;
import com.stepup.springrobot.model.communication.UpdateSDRequest;
import com.stepup.springrobot.model.mqtt.MqttMessageType;
import com.stepup.springrobot.model.robot.RobotFirmwareVersion;
import com.stepup.springrobot.repository.alarm.AlarmScheduleExecutionRepository;
import com.stepup.springrobot.repository.communication.CommunicationRequestRepository;
import com.stepup.springrobot.repository.communication.UpdateSDRequestRepository;
import com.stepup.springrobot.repository.game.RobotFirmwareVersionRepository;
import com.stepup.springrobot.security.JwtService;
import com.stepup.springrobot.service.CommonService;
import com.stepup.springrobot.service.MqttService;
import com.stepup.springrobot.service.SlackWarningSystemService;
import com.stepup.springrobot.service.UploadFileToS3;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;
import java.util.Random;

@Log4j2
@Service
public class ConnectService extends CommonService {
    @Value("${conversation_socket_endpoint}")
    private String conversationSocketEndpoint;

    @Autowired
    private CommunicationRequestRepository communicationRequestRepository;

    @Autowired
    private RobotFirmwareVersionRepository robotFirmwareVersionRepository;

    @Autowired
    private AlarmScheduleExecutionRepository alarmScheduleExecutionRepository;

    @Autowired
    private UpdateSDRequestRepository updateSDRequestRepository;

    @Autowired
    private MqttService mqttService;

    @Autowired
    private ObjectMapper objectMapper;

    protected ConnectService(ObjectMapper objectMapper, UploadFileToS3 uploadFileToS3, JwtService jwtService, SlackWarningSystemService slackWarningSystemService) {
        super(objectMapper, uploadFileToS3, jwtService, slackWarningSystemService);
    }

    public DataResponseDTO<WifiScanResDTO> scanWifi(HttpServletRequest request, String robotId) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        return new DataResponseDTO<>(CodeDefine.OK, "Gửi yêu cầu scan wifi thành công", WifiScanResDTO.builder()
                .requestId(handleRequestScanWifi(userId, robotId))
                .build());
    }

    private String handleRequestScanWifi(String userId, String robotId) throws JsonProcessingException {
        CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                .id(MqttMessageType.SCAN_WIFI.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .robotId(robotId)
                .profileId(userId)
                .type(MqttMessageType.SCAN_WIFI)
                .build());

        ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("request_id", communicationRequest.getId());
        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .message(MqttMessageDTO.builder()
                        .type(MqttMessageType.SCAN_WIFI)
                        .content(objectNode)
                        .build())
                .build());

        return communicationRequest.getId();
    }

    public DataResponseDTO<WifiScanStatusResDTO> getScanWifiResult(HttpServletRequest request, String requestId) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        CommunicationRequest communicationRequest = communicationRequestRepository.findById(requestId).orElse(null);
        if (communicationRequest == null || !userId.equals(communicationRequest.getUserId())) {
            throw new ContentNotFoundException("request_id", requestId);
        }

        communicationRequest = handleGetRequestData(communicationRequest, requestId);
        WifiScanStatusResDTO wifiStatusResDTO = objectMapper.readValue(communicationRequest.getReceivedData(), new TypeReference<>() {
        });

        communicationRequest.setResponseData(communicationRequest.getResponseData());
        communicationRequestRepository.save(communicationRequest);

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy kết quả scan wifi thành công", wifiStatusResDTO);
    }

    private CommunicationRequest handleGetRequestData(CommunicationRequest communicationRequest, String requestId) {
        // if communicationRequest.getResponseData() == null  check within 30 seconds with interval of 1 seconds
        int retryCount = 0;
        while (Objects.requireNonNull(communicationRequest).getReceivedData() == null) {
            retryCount++;
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("Error while waiting for response data", e);
            }

            communicationRequest = communicationRequestRepository.findById(requestId).orElse(null);
            if (retryCount >= 30) {
                break;
            }
        }

        if (Objects.requireNonNull(communicationRequest).getReceivedData() == null && communicationRequest.getType() != MqttMessageType.UPDATE_FIRMWARE) {
            throw new CommunicationTimeoutException();
        }

        return communicationRequest;
    }

    public DataResponseDTO<WifiScanResDTO> connectWifi(HttpServletRequest request, WifiConnectReqDTO connectReqDTO) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        String robotId = connectReqDTO.getRobotId();

        return new DataResponseDTO<>(CodeDefine.OK, "Gửi yêu cầu kết nối wifi thành công", WifiScanResDTO.builder()
                .requestId(handeRequestConnectWifi(userId, robotId, connectReqDTO))
                .build());
    }

    private String handeRequestConnectWifi(String userId, String robotId, WifiConnectReqDTO connectReqDTO) throws JsonProcessingException {
        CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                .id(MqttMessageType.CONNECT_WIFI.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .robotId(robotId)
                .profileId(userId)
                .type(MqttMessageType.CONNECT_WIFI)
                .build());

        ObjectNode content = objectMapper.createObjectNode();
        content.put("request_id", communicationRequest.getId());
        content.set("wifi", objectMapper.valueToTree(connectReqDTO));
        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .message(MqttMessageDTO.builder()
                        .type(MqttMessageType.CONNECT_WIFI)
                        .content(content)
                        .build())
                .build());

        return communicationRequest.getId();
    }

    public DataResponseDTO<WifiConnectResultDTO> getConnectWifiResult(HttpServletRequest request, String requestId) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        CommunicationRequest communicationRequest = communicationRequestRepository.findById(requestId).orElse(null);
        if (communicationRequest == null || !userId.equals(communicationRequest.getUserId())) {
            throw new ContentNotFoundException("request_id", requestId);
        }

        communicationRequest = handleGetRequestData(communicationRequest, requestId);
        WifiConnectResultDTO wifiStatusResDTO = objectMapper.readValue(communicationRequest.getReceivedData(), new TypeReference<>() {
        });

        communicationRequest.setResponseData(communicationRequest.getResponseData());
        communicationRequestRepository.save(communicationRequest);

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy kết quả communication wifi thành công", wifiStatusResDTO);
    }

    public DataResponseDTO<?> getTriggerDataByType(String triggerType) throws JsonProcessingException {
        if (MqttMessageType.WAKE_UP == MqttMessageType.from(triggerType)) {
            return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu trigger thành công", getWakeUpMessage());
        } else if (MqttMessageType.ALERT == MqttMessageType.from(triggerType)) {
            return new DataResponseDTO<>(CodeDefine.OK, "Lấy dữ liệu trigger thành công", getAlertMessage());
        }

        return new DataResponseDTO<>(400, "Không tìm thấy dữ liệu trigger type: " + triggerType);
    }

    private MqttMessageDTO getWakeUpMessage() throws JsonProcessingException {
        String animationData = "[{\"cmu\":\"expression\",\"start\":0.0,\"end\":0.28,\"duration\":null,\"animation_id\":0,\"animation\":\"trigger_mouth_0\"},{\"cmu\":\"expression\",\"start\":0.28,\"end\":0.42,\"duration\":null,\"animation_id\":3,\"animation\":\"trigger_mouth_3\"},{\"cmu\":\"expression\",\"start\":0.42,\"end\":0.7,\"duration\":null,\"animation_id\":5,\"animation\":\"trigger_mouth_5\"},{\"cmu\":\"expression\",\"start\":0.7,\"end\":0.91,\"duration\":null,\"animation_id\":6,\"animation\":\"trigger_mouth_6\"},{\"cmu\":\"expression\",\"start\":0.91,\"end\":1.13,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":1.13,\"end\":1.27,\"duration\":null,\"animation_id\":3,\"animation\":\"trigger_mouth_3\"},{\"cmu\":\"expression\",\"start\":1.27,\"end\":1.35,\"duration\":null,\"animation_id\":1,\"animation\":\"trigger_mouth_1\"},{\"cmu\":\"expression\",\"start\":1.35,\"end\":1.47,\"duration\":null,\"animation_id\":6,\"animation\":\"trigger_mouth_6\"},{\"cmu\":\"expression\",\"start\":1.47,\"end\":1.54,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":1.54,\"end\":1.68,\"duration\":null,\"animation_id\":4,\"animation\":\"trigger_mouth_4\"},{\"cmu\":\"expression\",\"start\":1.68,\"end\":1.75,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":1.75,\"end\":1.96,\"duration\":null,\"animation_id\":5,\"animation\":\"trigger_mouth_5\"},{\"cmu\":\"expression\",\"start\":1.96,\"end\":2.17,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":2.17,\"end\":2.38,\"duration\":null,\"animation_id\":3,\"animation\":\"trigger_mouth_3\"},{\"cmu\":\"expression\",\"start\":2.38,\"end\":2.65,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":2.65,\"end\":2.72,\"duration\":null,\"animation_id\":4,\"animation\":\"trigger_mouth_4\"},{\"cmu\":\"expression\",\"start\":2.72,\"end\":2.92,\"duration\":null,\"animation_id\":6,\"animation\":\"trigger_mouth_6\"},{\"cmu\":\"expression\",\"start\":2.92,\"end\":3.34,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":3.34,\"end\":3.56,\"duration\":null,\"animation_id\":0,\"animation\":\"trigger_mouth_0\"}]";
        ObjectNode animationNode = objectMapper.createObjectNode();
        animationNode.set("animations", objectMapper.readTree(animationData));
        animationNode.put("emotion", "Smile");
        animationNode.put("audio", "https://tts-file-mgc-52.hacknao.edu.vn/data/tts_male_Alloy/1d47d23d89808869bac5603576022ef6_1_1.mp3");
        animationNode.put("text", "Hello. It's time to start learning today. Are you ready?");
        return MqttMessageDTO.builder()
                .type(MqttMessageType.WAKE_UP)
                .content(animationNode)
                .build();
    }

    private MqttMessageDTO getAlertMessage() throws JsonProcessingException {
        String animationData = "[{\"cmu\":\"expression\",\"start\":0.0,\"end\":0.28,\"duration\":null,\"animation_id\":0,\"animation\":\"trigger_mouth_0\"},{\"cmu\":\"expression\",\"start\":0.28,\"end\":0.45,\"duration\":null,\"animation_id\":6,\"animation\":\"trigger_mouth_6\"},{\"cmu\":\"expression\",\"start\":0.45,\"end\":0.66,\"duration\":null,\"animation_id\":5,\"animation\":\"trigger_mouth_5\"},{\"cmu\":\"expression\",\"start\":0.66,\"end\":0.8,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":0.8,\"end\":0.88,\"duration\":null,\"animation_id\":1,\"animation\":\"trigger_mouth_1\"},{\"cmu\":\"expression\",\"start\":0.88,\"end\":1.0,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":1.0,\"end\":1.42,\"duration\":null,\"animation_id\":5,\"animation\":\"trigger_mouth_5\"},{\"cmu\":\"expression\",\"start\":1.42,\"end\":1.6,\"duration\":null,\"animation_id\":6,\"animation\":\"trigger_mouth_6\"},{\"cmu\":\"expression\",\"start\":1.6,\"end\":2.08,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":2.08,\"end\":2.24,\"duration\":null,\"animation_id\":1,\"animation\":\"trigger_mouth_1\"},{\"cmu\":\"expression\",\"start\":2.24,\"end\":2.28,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":2.28,\"end\":2.32,\"duration\":null,\"animation_id\":3,\"animation\":\"trigger_mouth_3\"},{\"cmu\":\"expression\",\"start\":2.32,\"end\":2.46,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":2.46,\"end\":2.53,\"duration\":null,\"animation_id\":3,\"animation\":\"trigger_mouth_3\"},{\"cmu\":\"expression\",\"start\":2.53,\"end\":2.67,\"duration\":null,\"animation_id\":2,\"animation\":\"trigger_mouth_2\"},{\"cmu\":\"expression\",\"start\":2.67,\"end\":2.75,\"duration\":null,\"animation_id\":1,\"animation\":\"trigger_mouth_1\"},{\"cmu\":\"expression\",\"start\":2.75,\"end\":3.18,\"duration\":null,\"animation_id\":5,\"animation\":\"trigger_mouth_5\"},{\"cmu\":\"expression\",\"start\":3.18,\"end\":3.27,\"duration\":null,\"animation_id\":0,\"animation\":\"trigger_mouth_0\"}]";
        ObjectNode animationNode = objectMapper.createObjectNode();
        animationNode.set("animations", objectMapper.readTree(animationData));
        animationNode.put("emotion", "Smile");
        animationNode.put("audio", "https://tts-file-mgc-52.hacknao.edu.vn/data/tts_male_Alloy/b8a7bd549e9e875303a4dccc914a5e08_1_1.mp3");
        animationNode.put("text", "You're score is a bit low. Let's try practicing more!");
        return MqttMessageDTO.builder()
                .type(MqttMessageType.ALERT)
                .content(animationNode)
                .build();
    }

    public DataResponseDTO<?> sendTestTriggerMessage(JsonNode data) throws JsonProcessingException {
        String triggerType = data.get("trigger_type").asText();
        MqttMessageType messageType = MqttMessageType.from(triggerType);
        if (messageType == null) {
            return new DataResponseDTO<>(400, "Không tồn tại trigger: " + triggerType);
        }

        String robotId;
        if (data.has("robot_id")) {
            robotId = data.get("robot_id").asText();
        } else {
            robotId = "robot_fake_2";
        }

        String userId = "abc";
        String topic = CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId;

        if (MqttMessageType.WAKE_UP == messageType) {
            mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                    .message(getWakeUpMessage())
                    .topic(topic)
                    .build());
        } else if (MqttMessageType.ALERT == messageType) {
            String audio;
            if (data.has("audio")) {
                audio = data.get("audio").asText();
            } else {
                audio = "https://smedia.stepup.edu.vn/robot/study/music/apt.mp3";
            }

            CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                    .id(MqttMessageType.ALERT.getType() + "_" + System.currentTimeMillis())
                    .userId(userId)
                    .robotId(robotId)
                    .profileId(userId)
                    .type(MqttMessageType.ALERT)
                    .status(CommunicationRequestStatusType.PENDING)
                    .build());
            ObjectNode message = JsonNodeFactory.instance.objectNode();
            message.put("request_id", communicationRequest.getId());
            ObjectNode dataContent = JsonNodeFactory.instance.objectNode();
            dataContent.put("audio", audio);
            message.set("data", dataContent);

            mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                    .message(MqttMessageDTO.builder()
                            .type(MqttMessageType.ALERT)
                            .content(message)
                            .build())
                    .topic(topic)
                    .build());
        } else if (MqttMessageType.SCAN_WIFI == messageType) {
            handleRequestScanWifi(userId, robotId);
        } else if (MqttMessageType.CONNECT_WIFI == messageType) {
            String wifiName = data.get("wifi_name").asText();
            String password = data.get("password").asText();
            handeRequestConnectWifi(userId, robotId, WifiConnectReqDTO.builder()
                    .name(wifiName)
                    .password(password)
                    .build());
        } else if (MqttMessageType.UPDATE_FIRMWARE == messageType) {
            String esp1 = null;
            if (data.has("esp1")) {
                esp1 = data.get("esp1").asText();
            }

            String esp2 = null;
            if (data.has("esp2")) {
                esp2 = data.get("esp2").asText();
            }

            String version = null;
            if (data.has("version")) {
                version = data.get("version").asText();
            }

            handleRequestUpdateFirmware(userId, robotId, esp1, esp2, version);
        } else if (MqttMessageType.MUSIC == messageType) {
            String audio;
            if (data.has("audio")) {
                audio = data.get("audio").asText();
            } else {
                audio = "https://smedia.stepup.edu.vn/robot/study/music/apt.mp3";
            }

            String image;
            if (data.has("image")) {
                image = data.get("image").asText();
            } else {
                image = "https://smedia.stepup.edu.vn/robot/mqtt/emotion.jpg";
            }

            handleRequestPlayMusicOrStory(userId, robotId, audio, image, MqttMessageType.MUSIC);
        } else if (MqttMessageType.STORY == messageType) {
            String audio;
            if (data.has("audio")) {
                audio = data.get("audio").asText();
            } else {
                audio = "https://smedia.stepup.edu.vn/robot/study/music/sotry.mp3";
            }

            String image;
            if (data.has("image")) {
                image = data.get("image").asText();
            } else {
                image = "https://smedia.stepup.edu.vn/robot/mqtt/emotion.jpg";
            }

            handleRequestPlayMusicOrStory(userId, robotId, audio, image, MqttMessageType.STORY);
        } else if (MqttMessageType.VOLUME == messageType) {
            int value;
            if (data.has("value")) {
                value = data.get("value").asInt();
            } else {
                // get random number from 0 to 21
                value = new Random().nextInt(100);
            }

            CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                    .id(MqttMessageType.VOLUME.getType() + "_" + System.currentTimeMillis())
                    .userId(userId)
                    .robotId(robotId)
                    .profileId(userId)
                    .type(MqttMessageType.VOLUME)
                    .status(CommunicationRequestStatusType.PENDING)
                    .build());
            ObjectNode message = JsonNodeFactory.instance.objectNode();
            message.put("request_id", communicationRequest.getId());
            ObjectNode dataContent = JsonNodeFactory.instance.objectNode();
            dataContent.put("value", value);
            message.set("data", dataContent);

            mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                    .message(MqttMessageDTO.builder()
                            .type(MqttMessageType.VOLUME)
                            .content(message)
                            .build())
                    .topic(topic)
                    .build());
        } else if (MqttMessageType.SCREEN_BRIGHTNESS == messageType) {
            int value;
            if (data.has("value")) {
                value = data.get("value").asInt();
            } else {
                // get random number from 0 to 100
                value = new Random().nextInt(100);
            }

            CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                    .id(MqttMessageType.SCREEN_BRIGHTNESS.getType() + "_" + System.currentTimeMillis())
                    .userId(userId)
                    .robotId(robotId)
                    .profileId(userId)
                    .type(MqttMessageType.SCREEN_BRIGHTNESS)
                    .status(CommunicationRequestStatusType.PENDING)
                    .build());
            ObjectNode message = JsonNodeFactory.instance.objectNode();
            message.put("request_id", communicationRequest.getId());

            ObjectNode dataContent = JsonNodeFactory.instance.objectNode();
            dataContent.put("value", value);
            message.set("data", dataContent);
            mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                    .message(MqttMessageDTO.builder()
                            .type(MqttMessageType.SCREEN_BRIGHTNESS)
                            .content(message)
                            .build())
                    .topic(topic)
                    .build());
        } else if (MqttMessageType.SCREEN_BACKGROUND == messageType) {
            CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                    .id(MqttMessageType.SCREEN_BACKGROUND.getType() + "_" + System.currentTimeMillis())
                    .userId(userId)
                    .robotId(robotId)
                    .profileId(userId)
                    .type(MqttMessageType.SCREEN_BACKGROUND)
                    .status(CommunicationRequestStatusType.PENDING)
                    .build());
            ObjectNode message = JsonNodeFactory.instance.objectNode();
            message.put("request_id", communicationRequest.getId());

            ObjectNode dataContent = JsonNodeFactory.instance.objectNode();
            // get random between BLACK and DARK_BLUE
            String random = new Random().nextBoolean() ? "BLACK" : "DARK_BLUE";
            dataContent.put("value", random);
            message.set("data", dataContent);
            mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                    .message(MqttMessageDTO.builder()
                            .type(MqttMessageType.SCREEN_BACKGROUND)
                            .content(message)
                            .build())
                    .topic(topic)
                    .build());
        } else if (MqttMessageType.SCREEN_EMOTION == messageType) {
            String value;
            if (data.has("value")) {
                value = data.get("value").asText();
            } else {
                // get random number from 0 to 100
                value = new Random().nextBoolean() ? "https://smedia.stepup.edu.vn/robot/mqtt/emotion.jpg" : "https://smedia.stepup.edu.vn/robot/mqtt/happy.jpg";
            }

            CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                    .id(MqttMessageType.SCREEN_EMOTION.getType() + "_" + System.currentTimeMillis())
                    .userId(userId)
                    .robotId(robotId)
                    .profileId(userId)
                    .type(MqttMessageType.SCREEN_EMOTION)
                    .status(CommunicationRequestStatusType.PENDING)
                    .build());
            ObjectNode message = JsonNodeFactory.instance.objectNode();
            message.put("request_id", communicationRequest.getId());

            ObjectNode dataContent = JsonNodeFactory.instance.objectNode();

            dataContent.put("value", value);
            message.set("data", dataContent);
            mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                    .message(MqttMessageDTO.builder()
                            .type(MqttMessageType.SCREEN_EMOTION)
                            .content(message)
                            .build())
                    .topic(topic)
                    .build());
        } else if (MqttMessageType.UPDATE_SD == messageType) {
            UpdateSDDetailDTO updateSDDetailDTO = objectMapper.convertValue(data.get("data"), new TypeReference<>() {
            });
            handleUpdateSDCardRequest(userId, robotId, updateSDDetailDTO);

        } else if (MqttMessageType.ASSIGN_LESSON == messageType) {
            String audio_format = data.get("audio_format").asText();
            long botId;
            if (data.has("bot_id")) {
                botId = data.get("bot_id").asLong();
            } else {
                // get random number from 0 to 100
                botId = 27L;
            }

            handleRequestAssignLesson(userId, botId, robotId, audio_format);
        } else if (MqttMessageType.FACE_DEMO == messageType) {
            String image;
            if (data.has("image")) {
                image = data.get("image").asText();
            } else {
                image = "https://smedia.stepup.edu.vn/robot/mqtt/emotion.jpg";
            }
            handleFaceDemoRequest(userId, robotId, image);
        } else if (MqttMessageType.ALARM == messageType) {
            LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
            String currentTime = now.format(CodeDefine.ALARM_SCHEDULE_PATTERN);
            handleAlarmRequest(robotId, userId, null, null, null, null, null, currentTime);
        } else if (MqttMessageType.DEMO_CUSTOM_EMOTIONS == messageType) {
            JsonNode messageData = null;
            if (data.has("data")) {
                messageData = data.get("data");
            }
            handleCustomEmotions(userId, robotId, messageData);
        }

        return new DataResponseDTO<>(CodeDefine.OK, "Gửi trigger thành công");
    }

    private void handleCustomEmotions(String userId, String robotId, JsonNode messageData) {
        try {
            String requestData = messageData == null ? objectMapper.writeValueAsString(objectMapper.readTree(CodeDefine.DEMO_CUSTOM_EMOTION_DATA)) : objectMapper.writeValueAsString(messageData) ;
            CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                    .id(MqttMessageType.DEMO_CUSTOM_EMOTIONS.getType() + "_" + System.currentTimeMillis())
                    .userId(userId)
                    .robotId(robotId)
                    .profileId(userId)
                    .type(MqttMessageType.DEMO_CUSTOM_EMOTIONS)
                    .status(CommunicationRequestStatusType.PENDING)
                    .requestData(requestData)
                    .build());
            ObjectNode data = objectMapper.createObjectNode();
            data.put("request_id", communicationRequest.getId());
            data.set("data", objectMapper.readTree(requestData));

            mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                    .message(MqttMessageDTO.builder()
                            .type(MqttMessageType.DEMO_CUSTOM_EMOTIONS)
                            .content(data)
                            .build())
                    .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                    .build());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void handleAlarmRequest(String robotId, String userId, String message, String remindAudio, String responseAudio, Long alarmScheduleId, AlarmActivityType activityType, String currentTime) throws JsonProcessingException {
        ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("message", message == null ? "Time to study" : message);
        objectNode.put("sound", "https://sufile.stepup.edu.vn/storage/robot/mqtt/alarm/alarm.mp3");
        objectNode.put("remind_voice", remindAudio == null ? "http://tts-file-mgc-42.hacknao.edu.vn/data/tts_female_linh_v1/a30b2ec7488be77fe83cbd407b64ee49_3_1.mp3" : remindAudio);
        objectNode.put("response_voice", responseAudio == null ? "http://tts-file-mgc-52.hacknao.edu.vn/data/tts_female_linh_v1/a67f01a7475a4cb99aa7b46e93dc4a8e_3_1.mp3" : responseAudio);
        AlarmScheduleExecution communicationRequest = alarmScheduleExecutionRepository.save(AlarmScheduleExecution.builder()
                .id(MqttMessageType.ALARM.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .profileId(userId)
                .robotId(robotId)
                .alarmScheduleId(alarmScheduleId)
                .activityType(activityType)
                .status(CommunicationRequestStatusType.PENDING)
                .requestData(objectMapper.writeValueAsString(objectNode))
                .timeSchedule(currentTime)
                .build());

        ObjectNode data = objectMapper.createObjectNode();
        data.put("request_id", communicationRequest.getId());
        data.set("data", objectNode);

        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .message(MqttMessageDTO.builder()
                        .type(MqttMessageType.ALARM)
                        .content(data)
                        .build())
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .build());
    }

    public void handleUpdateSDCardRequest(String userId, String robotId, UpdateSDDetailDTO updateSDDetailDTO) throws JsonProcessingException {
        updateSDDetailDTO.getResources().forEach(resource -> {
            UpdateSDRequest updateSDRequest = updateSDRequestRepository.save(UpdateSDRequest.builder()
                    .robotId(robotId)
                    .status(CommunicationRequestStatusType.PENDING)
                    .actionType(updateSDDetailDTO.getAction())
                    .dataType(updateSDDetailDTO.getType())
                    .url(resource.getUrl())
                    .path(resource.getPath())
                    .build());
            resource.setResourceRequestId(updateSDRequest.getId());
        });

        CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                .id(MqttMessageType.UPDATE_SD.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .robotId(robotId)
                .profileId(userId)
                .type(MqttMessageType.UPDATE_SD)
                .status(CommunicationRequestStatusType.PENDING)
                .requestData(objectMapper.writeValueAsString(updateSDDetailDTO))
                .build());
        UpdateSDReqDTO updateSDReqDTO = UpdateSDReqDTO.builder()
                .requestId(communicationRequest.getId())
                .data(updateSDDetailDTO)
                .build();

        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .message(MqttMessageDTO.builder()
                        .type(MqttMessageType.UPDATE_SD)
                        .content(updateSDReqDTO)
                        .build())
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .build());
    }

    public void handleFaceDemoRequest(String userId, String robotId, String image) throws JsonProcessingException {
        ObjectNode dataContent = JsonNodeFactory.instance.objectNode();
        dataContent.put("image", image);
        CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                .id(MqttMessageType.FACE_DEMO.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .robotId(robotId)
                .profileId(userId)
                .type(MqttMessageType.FACE_DEMO)
                .status(CommunicationRequestStatusType.PENDING)
                .requestData(objectMapper.writeValueAsString(dataContent))
                .build());

        ObjectNode message = JsonNodeFactory.instance.objectNode();
        message.put("request_id", communicationRequest.getId());
        message.set("data", dataContent);
        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .message(MqttMessageDTO.builder()
                        .type(MqttMessageType.FACE_DEMO)
                        .content(message)
                        .build())
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .build());
    }

    public DataResponseDTO<WifiScanResDTO> updateFirmware(HttpServletRequest request, String robotId) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        handleRequestUpdateFirmware(userId, robotId, null, null, null);
        return new DataResponseDTO<>(CodeDefine.OK, "Gửi yêu cầu update firmware thành công");
    }

    private void handleRequestUpdateFirmware(String userId, String robotId, String esp1, String esp2, String version) throws JsonProcessingException {
        CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                .id(MqttMessageType.UPDATE_FIRMWARE.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .robotId(robotId)
                .profileId(userId)
                .type(MqttMessageType.UPDATE_FIRMWARE)
                .status(CommunicationRequestStatusType.PENDING)
                .build());
        RobotFirmwareVersion latestVersion = robotFirmwareVersionRepository.getLatestFirmware();

        ObjectNode objectNode = objectMapper.createObjectNode();
        objectNode.put("request_id", communicationRequest.getId());
        objectNode.put("request_id", communicationRequest.getId());
        objectNode.put("version", version == null ? latestVersion.getVersion() : version);
        objectNode.put("esp1", esp1 == null ? latestVersion.getEsp1() : esp1);
        objectNode.put("esp2", esp2 == null ? latestVersion.getEsp2() : esp2);
        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .message(MqttMessageDTO.builder()
                        .type(MqttMessageType.UPDATE_FIRMWARE)
                        .content(objectNode)
                        .build())
                .build());
    }

    public void handleRequestAssignLesson(String userId, Long botId, String robotId, String audio_format) throws JsonProcessingException {
        ObjectNode data = objectMapper.createObjectNode();
        data.put("socket_endpoint", conversationSocketEndpoint + CodeDefine.SOCKET_VER_2_ENDPOINT + "?bot_id=" + botId + "&robot_id=" + robotId +"&audio_format=" + audio_format);

        CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                .id(MqttMessageType.ASSIGN_LESSON.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .robotId(robotId)
                .profileId(userId)
                .type(MqttMessageType.ASSIGN_LESSON)
                .status(CommunicationRequestStatusType.PENDING)
                .requestData(objectMapper.writeValueAsString(data))
                .build());

        ObjectNode content = objectMapper.createObjectNode();
        content.put("request_id", communicationRequest.getId());
        content.set("data", data);

        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .message(MqttMessageDTO.builder()
                        .type(MqttMessageType.ASSIGN_LESSON)
                        .content(content)
                        .build())
                .build());
    }

    public void handleRequestPlayMusicOrStory(String userId, String robotId, String audio, String image, MqttMessageType messageType) throws JsonProcessingException {
        ObjectNode dataContent = JsonNodeFactory.instance.objectNode();
        dataContent.put("audio", audio);
        dataContent.put("image", image);
        CommunicationRequest communicationRequest = communicationRequestRepository.save(CommunicationRequest.builder()
                .id(messageType.getType() + "_" + System.currentTimeMillis())
                .userId(userId)
                .robotId(robotId)
                .profileId(userId)
                .type(messageType)
                .status(CommunicationRequestStatusType.PENDING)
                .requestData(objectMapper.writeValueAsString(dataContent))
                .build());
        ObjectNode message = JsonNodeFactory.instance.objectNode();
        message.put("request_id", communicationRequest.getId());
        message.set("data", dataContent);

        mqttService.sendMessage(PublishMqttMessageReqDTO.builder()
                .message(MqttMessageDTO.builder()
                        .type(messageType)
                        .content(message)
                        .build())
                .topic(CodeDefine.ROBOT_MQTT_TOPIC_PREFIX + robotId)
                .build());
    }

    public DataResponseDTO<WifiConnectResultDTO> getUpdateFirmwareResult(HttpServletRequest request, String robotId) throws IOException {
        AccountInfoDTO userDataDTO = getAccountInfo(request, null, false);
        String userId = userDataDTO.getUserData().getUserId();
        CommunicationRequest communicationRequest = communicationRequestRepository.findFirstByRobotIdAndUserIdAndTypeOrderByIdDesc(robotId, userId, MqttMessageType.UPDATE_FIRMWARE);
        if (communicationRequest == null) {
            throw new ContentNotFoundException("request for robot", robotId);
        }

        communicationRequest = handleGetRequestData(communicationRequest, communicationRequest.getId());
        if (communicationRequest.getStatus() == CommunicationRequestStatusType.PENDING) {
            return new DataResponseDTO<>(CodeDefine.OK, "Lấy kết quả update firmware thành công", WifiConnectResultDTO.builder()
                    .status(CommunicationRequestStatusType.PENDING.getType())
                    .build());
        } else {
            communicationRequest.setResponseData(communicationRequest.getReceivedData());
        }

        WifiConnectResultDTO wifiStatusResDTO = objectMapper.readValue(communicationRequest.getReceivedData(), new TypeReference<>() {
        });

        communicationRequestRepository.save(communicationRequest);

        return new DataResponseDTO<>(CodeDefine.OK, "Lấy kết quả update firmware thành công", wifiStatusResDTO);
    }
}
