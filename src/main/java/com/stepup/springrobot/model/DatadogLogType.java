package com.stepup.springrobot.model;

import java.util.HashMap;
import java.util.Map;

public enum DatadogLogType {
    CONVERSATION_LLM_RESPONSE("CONVERSATION_LLM_RESPONSE"),
    CONVERSATION_FAST_RESPONSE("CONVERSATION_FAST_RESPONSE"),
    CONVERSATION_TTS("CONVERSATION_TTS"),
    CONVERSATION_UPLOAD_AUDIO("CONVERSATION_UPLOAD_AUDIO"),
    CONVERSATION_ANIMATION("CONVERSATION_ANIMATION"),
    CONVERSATION_SERVER_RESPONSE("CONVERSATION_SERVER_RESPONSE");

    private static final Map<String, DatadogLogType> TEXT_TO_SPEECH_VOICE_HASH_MAP = new HashMap<>();

    private final String feature;

    DatadogLogType(String feature) {
        this.feature = feature;
    }

    static {
        for (DatadogLogType e : values()) {
            TEXT_TO_SPEECH_VOICE_HASH_MAP.put(e.feature, e);
        }
    }

    public static DatadogLogType from(String feature) {
        return TEXT_TO_SPEECH_VOICE_HASH_MAP.get(feature);
    }

    public String getFeature() {
        return this.feature;
    }
}
