server.port=8888

# HTTP encoding (HttpEncodingProperties)
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

spring.datasource.url=
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=
spring.datasource.password=
spring.jpa.hibernate.ddl-auto=update
spring.jpa.generate-ddl=true
spring.jpa.show-sql=false
spring.jpa.open-in-view=false
logging.level.org.hibernate.SQL=TRACE

spring.devtools.livereload.enabled=false

#=====redis
spring.redis.host=redisrobot.tofu-dev.svc.cluster.local
spring.redis.password=@VNStepUp2022@1
spring.redis.port=6373

spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB

#token for cms
hacknao_token=eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxMSIsImlhdCI6MTU3OTQwNjA2MCwiZXhwIjoxNjEwOTQyMDYwfQ.GKN7GKTGPgTFV9I9WuAc8WSclFdYLSqF9nTFi19TOq4IdnN1ds_xrGHSHK_l856m8vX251a0S0uFWXLVNzJT_g

#alert noti system
hostname_wp_alert_system=https://wp.stepup.edu.vn:5443
path_wp_alert_system_critical=/hooks/3wza3rsww7dkicrh5gyhpxna6o

#set sentry collect error in project
sentry.dsn=https://<EMAIL>/4508458876862464
sentry.environment=development
# Set traces-sample-rate to 1.0 to capture 100% of transactions for performance monitoring.
# We recommend adjusting this value in production.
#sentry.traces-sample-rate=1.0
sentry_token=****************************************************************

s3.aws.hostname=https://storage.googleapis.com
s3.aws.access_key=GOOGCZHD6SQSXRS2BP3XXH7Z
s3.aws.secret=RRno4bBPRFzUEe6WZx0WrwV8M5KReVdwlK5XO9FP
s3.aws.bucket=sumedia
s3.aws.region=ASIA-SOUTHEAST1

cdn_domain=https://sustorage.stepup.edu.vn

#internal tts
internal_tts_host_name=http://************:8090
internal_tts_uri=/api/v1/tts
internal_tts_token=4a5vyvn37z4C5MNGYGsKw3dNo3Vdw4PG
tts_speed=1.0
default_tts_volume=3

#internal stt
internal_stt_host_name=http://************:6112
internal_stt_uri=/api/v1/asr/audio
internal_stt_token=su-ewiuwernmvf1ifmdsafajkdfaiwwe

#lypsync
lipsync_host_name=http://************:3001
lipsync_uri=/api/v1/getLipSync
lipsync_token=SU_OPEN_TKI9

#llm
llm_host_name=http://*************:9400
llm_init_uri=/personalized-ai-coach/api/v1/bot/initConversation
llm_webhook_uri=/personalized-ai-coach/api/v1/bot/webhook
llm_summary_uri=/robot-ai-lesson/api/v1/bot/summaryConversation

conversation_timeout_minutes=10

is_use_google_speech_to_text=false

#import data sheet
google_sheets_learn_data=1dliigW-knVb5YmWJt_pp2Y54nG1QcRI33MwfuKNM8Zg

#study data sheet names
study_unit_sheet_name=study_unit_dev
study_topic_sheet_name=study_topic_dev
study_lesson_sheet_name=study_lesson_dev

is_log_audio_chunk=false

#mqtt
mqtt_broker_url=tcp://************:32305
mqtt_client_id=72adb797-f829-1234-a436-a0613fece725
mqtt_username=admin
mqtt_password=12

mqtt_user_auth_token=123456

rate_limit_request_forgot_pw_max: 1000
rate_limit_request_signin=1000
rate_limit_request_signup=5
rate_limit_request_sms_max=5
rate_limit_verify_sms_max=5

sms_firebase_request=1
sms_brandname_request=1
is_enable_sms_otp_method=true
is_enable_zalo_otp_method=true
is_enable_zalo_otp_fns_method=true

sms_block_time=15

#sms gateway
sms_gateway_ip_whitelist=**************
sms_gateway_url=http://*************:8008
sms_gateway_device=db59-0401-2059-0095
sms_gateway_url_main=http://************:8008
sms_gateway_device_main=db59-a230-9040-0119
sms_gateway_url_4g=http://************:8009
sms_gateway_device_4g=dbd2-0228-9036-0019
sms_gateway_username=khiemnd5
sms_gateway_password=Nhung@21111997
sms_suffix_message_sms=la so kich hoat tai khoan PIKA App

zns.template.id=262398
zns.app.id=1903095160023928507
zns.app.secret_key=
fns.template.id=6010
fns.app.id=1716259965
fns.app.secret_key=

#SMS Brandname
brandname_username=stepupedu
brandname_password=Stepup#704
brandname_cpcode=STEPUPEDU
brandname_sender=STEP_UP_EDU
brandname_network_accept_send=VIETTEL,VIETNAMOBILE,VINA,MOBI,GTEL,OTHER
brandname_content_tf_sign_up=STEP_UP_EDU: %active_code% la ma (OTP) xac nhan dang ky tai khoan tren PIKA, ma se het han trong vong 05 phut. KHONG chia se voi bat ky ai.
brandname_content_tf_forgot_pw=STEP_UP_EDU: %active_code% la ma (OTP) xac nhan khoi phuc mat khau tren PIKA, ma se het han trong vong 05 phut. KHONG chia se voi bat ky ai.
brandname_content_purchase_confirmation=Step Up Edu xac nhan da nhan duoc chuyen khoan pre order san pham Robot Pika cua anh chi.\nChi tiet thong tin tai email. Step Up xin cam on.
#brandname_content_tc_sign_up=STEP UP - The Coach: %active_code% la ma (OTP) xac nhan dang ky tai khoan, ma se het han trong vong 05 phut. KHONG chia se voi bat ky ai.
#brandname_content_tc_forgot_pw=STEP UP - The Coach: %active_code% la ma (OTP) xac nhan khoi phuc mat khau, ma se het han trong vong 05 phut. KHONG chia se voi bat ky ai.

#token
access_token_duration_in_hours=2400
refresh_token_duration_in_days=300

rate_limit_check_speech_trial: 30
rate_limit_check_speech_open: 30
rate_limit_check_speech: 100

rate_limit_tts_trial=100
rate_limit_tts_open=50
rate_limit_tts=500

rate_limit_speech_to_text_trial=100
rate_limit_speech_to_text_open=100
rate_limit_speech_to_text=100

#hostname api speech
api_speech_hostname=http://************:8077
api_speech_hostname_background=http://************:8077
api_speech_hostname_backup=http://************:8077

api_speech_uri_v1=/api/stepsup/pronunciation
api_speech_token_v1=6aYANCAt5QIm4bdGNvOYaqLUl8jxLuWW

api_speech_uri_v2=/api/stepsup/pronunciation/multiple
api_speech_token_v2=ZHLIjJDZLbKZxN6TlMB411ae9N6q87G8

audio_socket_host=wss://streaming-wp.hacknao.edu.vn
test_audio_socket_host=ws://************:5007?language=vi

#ielts conversation
ielts_conversation_host=https://coachieltstest-api.stepup.edu.vn
ielts_conversation_user_id=5baa88a9-f6e4-44bf-84bf-4192f2eb5090
ielts_conversation_lesson_id=lesson_263.1

#llm conversation
default_bot_ids=20,18
is_use_llm_conversation=true
llm_communicate_token=b1812cb7-2513-408b-bb22-d9f91b099fbd

#fast response
fast_response_host=http://*************:25041
fast_response_uri=/predict

#ws
conversation_socket_endpoint=ws://robot-ws.hacknao.edu.vn

#web mvp
web_mvp_host=https://mvp-api.hacknao.edu.vn

# Swagger Configuration
swagger.openapi.local-url=
swagger.openapi.dev-url=https://robot-api.hacknao.edu.vn/
swagger.openapi.staging-url=
swagger.openapi.prod-url=

springdoc.packages-to-scan=com.stepup.springrobot.controller
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operations-sorter=alpha
springdoc.swagger-ui.tags-sorter=alpha
springdoc.swagger-ui.filter=true
springdoc.enable-spring-security=true
springdoc.swagger-ui.try-it-out-enabled=true
springdoc.swagger-ui.persistAuthorization=true
springdoc.show-actuator=true

# Swagger Authentication
swagger.auth.username=admin
swagger.auth.password=admin

mvp_host=http://localhost:8000

# set notification
app.firebase-configuration-file=pika-robot-dev-firebase-adminsdk-fbsvc-1075118274.json

#ladipage webhook
ladipage.target.product.id=90738

max_sign_in_device=3

message_payload_secret_key=jBXJZz34kmKcgu9wwdsOWbKum8kQpryWY9fOgmwT+lg=

test_gifs=https://smedia.stepup.edu.vn/robot/upload/gif/39071_2.gif,https://smedia.stepup.edu.vn/robot/upload/gif/87316_2.gif,https://smedia.stepup.edu.vn/robot/upload/gif/61773_2.gif,https://smedia.stepup.edu.vn/robot/upload/gif/listening_gif.gif
test_bot_id=288

stress_test_robot_id=robot_1,robot_2

# ASR gRPC Service Configuration
is_use_grpc_asr=false
asr_grpc_uri_vi=*************:9111
asr_grpc_uri_en=*************:9112

#extract facts
extract_facts_host=http://*************:9404
extract_facts_uri=/robot-ai-lesson/api/v1/bot/extractFacts
#retrieve fact
retrieve_facts_host=http://*************:6699
retrieve_facts_uri=/test/get_facts

#wake word detection
wake_word_detection_host=http://*************:9110
wake_word_detection_uri=/detect_opus_frames?threshold=0.99

#datadog
datadog_api_key=abc
datadog_application_key=abc
datadog_endpoint=https://http-intake.logs.us5.datadoghq.com/api/v2/logs
datadog_host=robot-dev
datadog_service=robot-dev