<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <title>Chat History</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css"/>
    <link rel="stylesheet" href="/css/json-viewer.css"/>
    <style>
        .chat-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 12px;
            margin: 4px 0;
            position: relative;
            padding-bottom: 40px;
        }

        .media-container {
            margin-top: 8px;
            max-width: 100%;
        }

        .media-container img,
        .media-container video {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
        }

        .message-time {
            font-size: 0.8em;
            color: #666;
            margin: 0 8px;
        }

        .message-lang {
            font-size: 0.8em;
            color: #666;
            margin: 0 8px;
        }

        .user-message {
            align-items: flex-end;
        }

        .bot-message {
            align-items: flex-start;
        }

        .user-message .message-content {
            background: #e9ecef;
            color: #212529;
            border-bottom-right-radius: 4px;
        }

        .bot-message .message-content {
            background: #e9ecef;
            color: #212529;
            border-bottom-left-radius: 4px;
        }

        .header {
            background: #007bff;
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }

        .chat-header {
            position: sticky;
            top: 0;
            background: white;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
            z-index: 1000;
        }

        .audio-player {
            margin-top: 8px;
            width: 250px;
            margin-bottom: 8px;
        }

        .mode-switch {
            margin-left: 10px;
            display: inline-flex;
            align-items: center;
        }

        .realtime-indicator {
            color: #28a745;
            display: none;
            margin-left: 8px;
        }

        .realtime-indicator.active {
            display: inline;
        }

        .report-icon {
            cursor: pointer;
            color: #dc3545;
            margin-left: 8px;
            opacity: 0.6;
        }

        .report-icon:hover {
            opacity: 1;
        }

        /* Styles for like/dislike buttons */
        .message-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 8px;
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: inherit;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .action-button {
            background: none;
            border: none;
            padding: 4px;
            display: flex;
            align-items: center;
            gap: 4px;
            color: #666;
            font-size: 1em;
            cursor: pointer;
        }

        .action-button i {
            font-size: 1em;
        }

        .action-count {
            font-size: 0.9em;
        }

        .action-button:hover {
            opacity: 0.8;
        }

        /* Styles cho modal report */
        .report-modal .modal-dialog {
            max-width: 400px;
            margin: 1.75rem auto;
        }

        .report-modal .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .report-modal .modal-header {
            padding: 20px 24px;
        }

        .report-modal .modal-body {
            padding: 24px;
        }

        .report-modal .form-check {
            padding: 12px 20px;
            margin: 0 -12px 8px -12px;
            border-radius: 10px;
        }

        .report-modal .form-check-label {
            font-size: 1rem;
            padding-left: 12px;
        }

        .report-modal .form-check-input {
            width: 20px;
            height: 20px;
            margin-top: 2px;
        }

        .report-modal #otherReasonInput {
            padding: 0 8px;
        }

        .report-modal #otherReasonText {
            padding: 10px 16px;
            font-size: 1rem;
        }

        .report-modal .btn-submit {
            padding: 10px 28px;
            font-size: 1rem;
        }

        /* Add pronunciation score styles */
        .pronunciation-score {
            margin-top: 8px;
            font-size: 0.9em;
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 8px;
            color: #212529;
        }

        .regenerate-section {
            margin-top: 8px;
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .regenerate-input {
            flex: 1;
            padding: 4px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .regenerate-btn {
            padding: 4px 8px;
            font-size: 0.9em;
            white-space: nowrap;
        }

        .total-score {
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 4px;
            display: block;
            margin-bottom: 8px;
        }

        .total-score-green {
            background-color: rgba(40, 167, 69, 0.15);
            color: #28a745;
        }

        .total-score-yellow {
            background-color: rgba(255, 193, 7, 0.15);
            color: #ffc107;
        }

        .total-score-red {
            background-color: rgba(220, 53, 69, 0.15);
            color: #dc3545;
        }

        .word-group {
            display: inline-block;
            margin-right: 8px;
            padding: 2px 4px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.5);
        }

        .letter-score {
            display: inline;
            padding: 0;
            margin: 0;
            transition: all 0.2s ease;
        }

        .letter-score:hover {
            transform: scale(1.2);
            z-index: 1;
        }

        .score-red {
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.15);
        }

        .score-yellow {
            color: #ffc107;
            background-color: rgba(255, 193, 7, 0.15);
        }

        .score-green {
            color: #28a745;
            background-color: rgba(40, 167, 69, 0.15);
        }

        .generate-score-btn {
            font-size: 0.8em;
            padding: 2px 8px;
            margin-top: 4px;
        }

        /* ASR QC boolean icon buttons - monochrome, no border, clearer active state */
        .boolean-icon-group .btn-icon {
            border: none !important;
            background: transparent !important;
            color: #6c757d; /* gray */
            padding: 0.35rem 0.6rem;
            line-height: 1;
            transition: color 0.15s ease, background-color 0.15s ease, transform 0.1s ease, box-shadow 0.15s ease;
            border-radius: 6px;
        }

        .boolean-icon-group .btn-icon i {
            font-size: 1.25rem; /* bigger icons */
        }

        .boolean-icon-group .btn-icon.active {
            color: #0d6efd; /* bootstrap primary */
            background: rgba(13, 110, 253, 0.20); /* blue fill when active */
            text-shadow: none;
        }

        .boolean-icon-group .btn-icon:hover {
            color: #000;
            background: rgba(0, 0, 0, 0.12);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .boolean-icon-group .btn-icon:active {
            transform: scale(0.96);
        }

        .boolean-icon-group .btn-icon:focus,
        .boolean-icon-group .btn-icon:focus-visible {
            box-shadow: none;
            outline: none;
        }

        /* Facts container styling */
        #factsContainer {
            max-height: 600px;
            /* Approximately 15 facts */
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }

        #factsList .list-group-item {
            border-left: none;
            border-right: none;
            border-radius: 0;
        }

        #factsList .list-group-item:first-child {
            border-top: none;
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
        }

        #factsList .list-group-item:last-child {
            border-bottom: none;
            border-bottom-left-radius: 0.375rem;
            border-bottom-right-radius: 0.375rem;
        }
    </style>
</head>

<body>
<div class="header">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center">
            <h2>Chat History</h2>
            <a href="/web/admin/conversations" class="btn btn-outline-light">Back to List</a>
        </div>
    </div>
</div>

<div class="container">
    <div class="chat-container">
        <div class="chat-header mb-4">
            <div class="d-flex flex-column">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5>
                        Conversation with User
                        <span th:text="${conversation.userId}"></span>
                    </h5>
                    <div class="mode-switch" style="display: none">
                        <input type="checkbox" id="realtimeToggle" class="form-check-input"/>
                        <label class="form-check-label ms-2">Real-time Mode</label>
                        <span class="realtime-indicator">●&nbsp;Live</span>
                    </div>

                    <!-- Expand all ASR QC forms toggle -->
                    <div class="ms-3 d-inline-flex align-items-center">
                        <input type="checkbox" id="expandAllAsrQc" class="form-check-input"/>
                        <label for="expandAllAsrQc" class="form-check-label ms-2">Expand all ASR QC</label>
                    </div>


                </div>

                <div class="d-flex flex-wrap gap-3 mb-2">
                    <div class="btn-group me-3">
                        <button th:if="${conversation.video != null && conversation.video != ''}"
                                class="btn btn-outline-primary" onclick="showVideoModal()">
                            <i class="bi bi-camera-video"></i> View Video
                        </button>
                        <button th:if="${conversation.log != null && conversation.log != ''}"
                                class="btn btn-outline-primary" onclick="showLogModal()">
                            <i class="bi bi-journal-text"></i> View Log
                        </button>
                        <button th:if="${conversation.serverLog != null && conversation.serverLog != ''}"
                                class="btn btn-outline-primary" onclick="showServerLogModal()">
                            <i class="bi bi-server"></i> Server Log
                        </button>
                        <button class="btn btn-outline-primary" onclick="showProfileModal()"
                                th:data-robot-id="${conversation.robotId}">
                            <i class="bi bi-person-circle"></i> Profile Info
                        </button>
                    </div>

                    <div class="btn-group">
                        <button id="generateReportBtn" class="btn btn-outline-info" onclick="generateReport()">
                            <i class="bi bi-file-earmark-bar-graph"></i> Generate Report
                        </button>
                        <button id="downloadExcelBtn" class="btn btn-outline-success"
                                onclick="downloadUserMessagesExcel()">
                            <i class="bi bi-file-earmark-excel"></i> Download User
                            Messages
                        </button>
                    </div>
                </div>

                <small class="text-muted"
                       th:text="${conversation.botType + ' - Bot ' + conversation.botId + ' - STT ' + conversation.asrType + ' - ' + conversation.conversationSource + ' - Phone: ' + conversation.phone + '-RobotId: ' + conversation.robotId+ ' - id ' + conversation.id}"
                       th:attr="data-phone=${conversation.phone}">
                </small>
                <div id="totalFeedback" class="mt-2">
                        <span class="badge bg-success me-2">
                            <i class="bi bi-hand-thumbs-up"></i>
                            <span th:text="${likes}" id="totalLikes">0</span>
                        </span>
                    <span class="badge bg-danger">
                            <i class="bi bi-hand-thumbs-down"></i>
                            <span th:text="${dislikes}" id="totalDislikes">0</span>
                        </span>
                </div>
            </div>
        </div>

        <div class="chat-messages" id="chat-messages">
            <div th:each="msg : ${messages}"
                 th:class="${msg.character.name() == 'USER'} ? 'message user-message' : 'message bot-message'"
                 th:data-message-id="${msg.id}">
                <div class="message-content">
                    <div class="d-flex justify-content-between align-items-start">
                        <p class="mb-0" th:text="${msg.content}"></p>
                        <i class="bi bi-exclamation-triangle-fill report-icon" th:if="${msg.dataReport != null}"
                           th:data-message-id="${msg.id}" th:data-message-content="${msg.content}"
                           th:data-report="${msg.dataReport}"
                           onclick="showDataReportModal(this.dataset.report)"></i>
                        <i class="bi bi-exclamation-triangle-fill report-icon" th:if="${msg.dataReport == null}"
                           onclick="showReportModal(this)" th:data-message-id="${msg.id}"
                           th:data-message-content="${msg.content}"></i>
                    </div>

                    <!-- Add pronunciation score section -->
                    <div th:if="${msg.character.name() == 'USER'}" class="pronunciation-score">
                        <div th:if="${msg.score != null}" class="pronunciation-score-content"
                             th:attr="data-score=${msg.score}">
                            <!-- Will be populated by JavaScript -->
                        </div>
                        <button th:if="${msg.score == null}"
                                class="btn btn-sm btn-outline-primary generate-score-btn" th:data-message-id="${msg.id}"
                                onclick="generatePronunciationScore(this.dataset.messageId)">
                            <i class="bi bi-mic"></i> Generate Pronunciation Score
                        </button>
                    </div>

                    <div th:if="${msg.audio}" class="audio-player">
                        <audio controls class="w-100" th:id="'audio-' + ${msg.id}" 
                               th:attr="crossorigin=${msg.character.name() == 'USER'} ? 'anonymous' : null">
                            <source th:src="${msg.audio}" type="audio/mpeg"/>
                        </audio>
                        <div th:if="${msg.character.name() == 'USER'}" class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                    th:attr="data-audio-id='audio-' + ${msg.id}" onclick="increaseGain(this)">
                                <i class="bi bi-volume-up"></i> Tăng Gain
                            </button>
                            <select class="form-select form-select-sm d-inline-block w-auto ms-2"
                                    th:attr="id='gain-select-' + ${msg.id}">
                                <option value="1">1x</option>
                                <option value="2">2x</option>
                                <option value="3">3x</option>
                                <option value="5">5x</option>
                                <option value="10">10x</option>
                                <option value="20">20x</option>
                                <option value="50">50x</option>
                                <option value="100">100x</option>
                            </select>
                        </div>
                    </div>
                    <!-- ASR QC inline form for USER messages -->
                    <div th:if="${msg.character.name() == 'USER'}" class="asr-qc mt-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary asr-qc-toggle"
                                onclick="toggleAsrQcForm(this)" th:data-message-id="${msg.id}">
                            <i class="bi bi-sliders"></i> ASR QC
                        </button>
                        <div class="asr-qc-form d-none mt-2 p-2 border rounded">
                            <div class="row g-2 align-items-center">
                                <div class="col-12 d-flex align-items-center gap-2">
                                    <span class="form-check-label me-2">ASR correct</span>
                                    <div class="btn-group btn-group-sm boolean-icon-group" role="group"
                                         data-target="is_asr_correct">
                                        <button type="button" class="btn btn-icon"
                                                th:classappend="${msg.isAsrCorrect} ? ' active' : ''"
                                                onclick="setBooleanIcon(this, true)">
                                            <i class="bi bi-hand-thumbs-up"></i>
                                        </button>
                                        <button type="button" class="btn btn-icon"
                                                th:classappend="${msg.isAsrCorrect} == false ? ' active' : ''"
                                                onclick="setBooleanIcon(this, false)">
                                            <i class="bi bi-hand-thumbs-down"></i>
                                        </button>
                                    </div>
                                    <input type="hidden" data-field="is_asr_correct" th:value="${msg.isAsrCorrect}"/>
                                </div>
                                <div class="col-12">
                                    <input type="text" class="form-control form-control-sm"
                                           placeholder="ASR corrected content"
                                           th:value="${msg.asrCorrectedContent ?: ''}"
                                           data-field="asr_corrected_content"/>
                                </div>
                                <div class="col-12 d-flex align-items-center gap-2">
                                    <span class="form-check-label me-2">Intent correct</span>
                                    <div class="btn-group btn-group-sm boolean-icon-group" role="group"
                                         data-target="is_intent_affected">
                                        <button type="button" class="btn btn-icon"
                                                th:classappend="${msg.isIntentAffected} ? ' active' : ''"
                                                onclick="setBooleanIcon(this, true)">
                                            <i class="bi bi-hand-thumbs-up"></i>
                                        </button>
                                        <button type="button" class="btn btn-icon"
                                                th:classappend="${msg.isIntentAffected} == false ? ' active' : ''"
                                                onclick="setBooleanIcon(this, false)">
                                            <i class="bi bi-hand-thumbs-down"></i>
                                        </button>
                                    </div>
                                    <input type="hidden" data-field="is_intent_affected"
                                           th:value="${msg.isIntentAffected}"/>
                                </div>
                                <div class="col-12">
                                    <input type="text" class="form-control form-control-sm"
                                           placeholder="Intent corrected content"
                                           th:value="${msg.intentCorrectedContent ?: ''}"
                                           data-field="intent_corrected_content"/>
                                </div>
                            </div>
                            <div class="text-end mt-2">
                                <button class="btn btn-sm btn-primary" onclick="submitAsrQcInline(this)"
                                        th:data-message-id="${msg.id}">
                                    <i class="bi bi-check2-circle"></i> Save
                                </button>
                            </div>
                        </div>
                    </div>
                    <div th:if="${msg.image}" class="media-container">
                        <img th:src="${msg.image}" alt="Message Image"/>
                    </div>
                    <div th:if="${msg.video}" class="media-container">
                        <video controls>
                            <source th:src="${msg.video}" type="video/mp4"/>
                            Your browser does not support the video tag.
                        </video>
                    </div>
                    <div class="message-actions" th:if="${msg.character.name() != 'USER'}">
                        <div class="action-button">
                            <i class="bi bi-hand-thumbs-up"></i>
                            <span class="action-count" th:text="${msg.likes != null ? msg.likes : 0}">0</span>
                        </div>
                        <div class="action-button">
                            <i class="bi bi-hand-thumbs-down"></i>
                            <span class="action-count" th:text="${msg.dislikes != null ? msg.dislikes : 0}">0</span>
                        </div>
                    </div>
                    <div class="message-lang"
                         th:text="'Lang: ' + ${msg.language ?: 'null'}"></div>
                </div>
                <div class="message-time" th:text="${#dates.format(msg.createdAt, 'HH:mm:ss dd-MM-yyyy')}"></div>
            </div>
        </div>

        <div id="loading" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <!-- Overlay loading -->
        <div id="loadingOverlay" style="
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
          ">
            <div style="
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
            ">
                <div class="spinner-border text-light" style="width: 3rem; height: 3rem" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <p class="text-light mt-2">Generating report, please wait...</p>
            </div>
        </div>

        <script th:inline="javascript">
            let currentPage = 1;
            let loading = false;
            let hasMore = true;
            let realtimeMode = false;
            let realtimeInterval;
            let lastMessageId = null;
            const conversationId = /*[[${conversation.id}]]*/ "";
            console.log("conversationId", conversationId);

            // Fallback: If conversationId is empty, try to extract it from the URL
            function getConversationIdFromUrl() {
                if (conversationId && conversationId !== "") return conversationId;

                const pathParts = window.location.pathname.split("/");
                // URL pattern is expected to be /web/admin/conversations/{id}
                for (let i = 0; i < pathParts.length; i++) {
                    if (
                        pathParts[i] === "conversations" &&
                        i + 1 < pathParts.length
                    ) {
                        const potentialId = pathParts[i + 1];
                        // Make sure it's a number
                        if (!isNaN(potentialId) && potentialId !== "") {
                            console.log(
                                "Extracted conversationId from URL:",
                                potentialId
                            );
                            return potentialId;
                        }
                    }
                }
                console.error("Could not determine conversation ID");
                return null;
            }

            document.addEventListener("DOMContentLoaded", function () {
                const messages = document.querySelectorAll(".message");
                if (messages.length > 0) {
                    const lastMessage = messages[messages.length - 1];
                    lastMessageId = lastMessage.dataset.messageId;
                }
            });

            function createMessageElement(msg) {
                const messageDiv = document.createElement("div");
                messageDiv.className =
                    msg.character === "USER"
                        ? "message user-message"
                        : "message bot-message";
                messageDiv.dataset.messageId = msg.id;

                const date = new Date(msg.createdAt);
                const formattedDate =
                    date.toLocaleTimeString("en-GB") +
                    " " +
                    date.getDate().toString().padStart(2, "0") +
                    "-" +
                    (date.getMonth() + 1).toString().padStart(2, "0") +
                    "-" +
                    date.getFullYear();

                const mediaContent = msg.image
                    ? `
                <div class="media-container">
                    <img src="${msg.image}" alt="Message Image">
                </div>
            `
                    : msg.video
                        ? `
                <div class="media-container">
                    <video controls>
                        <source src="${msg.video}" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                </div>
            `
                        : "";

                const content = `
                <div class="message-content">
                    <div class="d-flex justify-content-between align-items-start">
                        <p class="mb-0">${msg.content}</p>
                        <i class="bi bi-exclamation-triangle-fill report-icon"
                           onclick="showReportModal(this)"
                           data-message-id="${msg.id}"
                           data-message-content="${msg.content}"></i>
                    </div>
                    ${msg.audio
                    ? `
                        <div class="audio-player">
                            <audio controls class="w-100">
                                <source src="${msg.audio}" type="audio/mpeg">
                            </audio>
                        </div>
                    `
                    : ""
                }
                    ${mediaContent}
                </div>
                <div class="message-time">${formattedDate}</div>
            `;

                messageDiv.innerHTML = content;
                return messageDiv;
            }

            function getMaxMessageId() {
                const messages = document.querySelectorAll(".message");
                let maxId = 0;
                messages.forEach((message) => {
                    const messageId = parseInt(message.dataset.messageId);
                    if (messageId > maxId) {
                        maxId = messageId;
                    }
                });
                return maxId || null;
            }

            function fetchNewMessages() {
                const effectiveConversationId = getConversationIdFromUrl();
                if (!effectiveConversationId) {
                    console.error(
                        "Cannot fetch messages: No conversation ID available"
                    );
                    return;
                }

                const url = new URL(
                    `/web/admin/conversations/${effectiveConversationId}/messages`,
                    window.location.origin
                );
                url.searchParams.append("realtime", "true");

                // Always get the current maximum ID
                const currentMaxId = getMaxMessageId();
                if (currentMaxId) {
                    url.searchParams.append("afterId", currentMaxId);
                }

                fetch(url)
                    .then((response) => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then((data) => {
                        const chatMessages = document.getElementById("chat-messages");
                        let newMessagesAdded = false;

                        if (data.content && data.content.length > 0) {
                            // Get all existing message IDs
                            const existingMessageIds = new Set();
                            document.querySelectorAll(".message").forEach((msg) => {
                                existingMessageIds.add(msg.dataset.messageId);
                            });

                            // Append only new messages that don't already exist
                            data.content.forEach((msg) => {
                                if (!existingMessageIds.has(msg.id.toString())) {
                                    const messageElement = createMessageElement(msg);
                                    chatMessages.appendChild(messageElement);
                                    newMessagesAdded = true;
                                }
                            });

                            // Only scroll if new messages were actually added
                            if (newMessagesAdded) {
                                // Always scroll to bottom in real-time mode
                                if (realtimeMode) {
                                    window.scrollTo({
                                        top: document.body.scrollHeight,
                                        behavior: "smooth",
                                    });
                                } else {
                                    // Auto-scroll to bottom only if near bottom when not in real-time mode
                                    const isNearBottom =
                                        window.innerHeight + window.scrollY >=
                                        document.body.offsetHeight - 1000;
                                    if (isNearBottom) {
                                        window.scrollTo({
                                            top: document.body.scrollHeight,
                                            behavior: "smooth",
                                        });
                                    }
                                }
                            }
                        }
                    })
                    .catch((error) => {
                        console.error("Error fetching new messages:", error);
                        // If there's an error, don't stop the real-time updates
                        // The next interval will try again
                    });
            }

            function toggleRealtimeMode(enabled) {
                const indicator = document.querySelector(".realtime-indicator");
                if (enabled) {
                    realtimeMode = true;
                    indicator.classList.add("active");
                    realtimeInterval = setInterval(fetchNewMessages, 5000);
                } else {
                    realtimeMode = false;
                    indicator.classList.remove("active");
                    clearInterval(realtimeInterval);
                }
            }

            // Add event listener for the toggle button
            document
                .getElementById("realtimeToggle")
                .addEventListener("change", (e) => {
                    toggleRealtimeMode(e.target.checked);
                });
        </script>

        <script>
            // Handle audio playback
            document.addEventListener(
                "play",
                function (e) {
                    // Get all audio elements
                    const audios = document.getElementsByTagName("audio");

                    // Pause all audio elements except the one that just started playing
                    for (let audio of audios) {
                        if (audio !== e.target) {
                            audio.pause();
                            audio.currentTime = 0;
                        }
                    }
                },
                true
            ); // Use capture phase to ensure this runs before other event listeners
        </script>

        <script th:inline="javascript">
            document.addEventListener("DOMContentLoaded", function () {
                const conversationCreatedAt = new Date(
                    /*[[${conversation.createdAt}]]*/ ""
                );
                const thirtyMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);

                // Show real-time toggle only if conversation is less than 30 minutes old
                if (conversationCreatedAt > thirtyMinutesAgo) {
                    document.querySelector(".mode-switch").style.display =
                        "inline-flex";
                }
            });
        </script>
    </div>
</div>

<!-- Add Modal for Log -->
<div class="modal fade" id="logModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Conversation Log</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="logContent" style="white-space: pre-wrap"></pre>
            </div>
        </div>
    </div>
</div>

<!-- Add this before the closing body tag -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script th:inline="javascript">
    const conversationLog = /*[[${conversation.log}]]*/ "";
    const logModal = new bootstrap.Modal(document.getElementById("logModal"));

    function showLogModal() {
        try {
            const logContent = document.getElementById("logContent");
            const parsedLog = JSON.parse(conversationLog);
            logContent.textContent = JSON.stringify(parsedLog, null, 2);
            logModal.show();
        } catch (e) {
            console.error("Error parsing log:", e);
            // If JSON parsing fails, show raw log
            document.getElementById("logContent").textContent = conversationLog;
            logModal.show();
        }
    }
</script>

<!-- Thêm modal report vào cuối file, trước closing body tag -->
<div class="modal fade report-modal" id="reportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report conversation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="reportForm">
                    <input type="hidden" id="reportMessageId"/>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="reportReason" id="unclear_audio"
                               value="Speech-to-text sai"/>
                        <label class="form-check-label" for="unclear_audio">
                            Speech-to-text sai
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="reportReason" id="no_play"
                               value="User nói nhưng không nghe được"/>
                        <label class="form-check-label" for="no_play">
                            User nói nhưng không nghe được
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="reportReason" id="wrong_text"
                               value="S2T nhầm ngôn ngữ (Anh ra Việt, Việt ra Anh)"/>
                        <label class="form-check-label" for="wrong_text">
                            S2T nhầm ngôn ngữ (Anh ra Việt, Việt ra Anh)
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="reportReason" id="other" value="other"/>
                        <label class="form-check-label" for="other"> Khác </label>
                    </div>
                    <div id="otherReasonInput" class="mt-4 d-none">
                        <input type="text" class="form-control" id="otherReasonText" placeholder="Nhập lý do..."/>
                    </div>
                    <div class="text-end mt-4">
                        <button type="button" class="btn btn-submit" onclick="submitReport()">
                            SEND
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Cập nhật JavaScript cho chức năng report -->
<script>
    const reportModal = new bootstrap.Modal(
        document.getElementById("reportModal")
    );

    // Thêm event listener để hiển thị/ẩn text input khi chọn "Khác"
    document
        .querySelectorAll('input[name="reportReason"]')
        .forEach((radio) => {
            radio.addEventListener("change", function () {
                const otherReasonInput =
                    document.getElementById("otherReasonInput");
                if (this.value === "other") {
                    otherReasonInput.classList.remove("d-none");
                } else {
                    otherReasonInput.classList.add("d-none");
                }
            });
        });

    function showReportModal(element) {
        const messageId = element.dataset.messageId;
        document.getElementById("reportMessageId").value = messageId;
        document.getElementById("reportForm").reset();
        document.getElementById("otherReasonInput").classList.add("d-none");
        reportModal.show();
    }

    function submitReport() {
        const messageId = document.getElementById("reportMessageId").value;
        const selectedReason = document.querySelector(
            'input[name="reportReason"]:checked'
        );

        if (!selectedReason) {
            alert("Vui lòng chọn một lý do");
            return;
        }

        let reason = selectedReason.value;
        if (reason === "other") {
            const otherReason = document
                .getElementById("otherReasonText")
                .value.trim();
            if (!otherReason) {
                alert("Vui lòng nhập lý do");
                return;
            }
            reason = otherReason;
        }

        fetch(`/robot/api/v1/robot-open/personal/report`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                messageId: messageId,
                message: reason,
            }),
        })
            .then((response) => {
                if (response.ok) {
                    alert("Đã gửi report thành công");
                    reportModal.hide();
                    location.reload();
                } else {
                    alert("Gửi report thất bại");
                    reportModal.hide();
                    location.reload();
                }
            })
            .catch((error) => {
                console.error("Error submitting report:", error);
                alert("Đã xảy ra lỗi khi gửi báo cáo");
            });
    }

    function showDataReportModal(dataReport) {
        console.log("Data Report:", dataReport);
        document.getElementById("dataReportContent").textContent =
            dataReport || "No data available";
        const dataReportModal = new bootstrap.Modal(
            document.getElementById("dataReportModal")
        );
        dataReportModal.show();
    }
</script>

<!-- Modal for Data Report -->
<div class="modal fade" id="dataReportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Data Report</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="dataReportContent" style="white-space: pre-wrap"></pre>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Video -->
<div class="modal fade" id="videoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Conversation Video</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="ratio ratio-16x9">
                    <video id="videoPlayer" controls>
                        <source id="videoSource" src="" type="video/mp4"/>
                        Your browser does not support the video tag.
                    </video>
                </div>
            </div>
        </div>
    </div>
</div>

<script th:inline="javascript">
    const conversationVideo = /*[[${conversation.video}]]*/ "";
    const videoModal = new bootstrap.Modal(
        document.getElementById("videoModal")
    );

    function showVideoModal() {
        const videoSource = document.getElementById("videoSource");
        videoSource.src = conversationVideo;

        const videoPlayer = document.getElementById("videoPlayer");
        videoPlayer.load();

        videoModal.show();
    }
</script>

<script>
    // Function to mask the first 5 digits of a phone number
    function maskPhoneNumber(phone) {
        if (!phone) return "";
        // Handle phone numbers that start with a plus sign
        return phone
            .toString()
            .replace(/^(\+?\d{0,5})(.*)/, function (match, p1, p2) {
                // If the phone starts with +, keep the + and mask the next 5 characters
                if (p1.startsWith("+")) {
                    return "+" + "*".repeat(Math.min(5, p1.length - 1)) + p2;
                }
                // Otherwise mask the first 5 characters
                return "*".repeat(Math.min(5, p1.length)) + p2;
            });
    }

    // Immediately invoked function to mask phone numbers
    (function () {
        // Wait for DOM to be fully loaded
        if (document.readyState === "loading") {
            document.addEventListener("DOMContentLoaded", maskPhoneInHeader);
        } else {
            maskPhoneInHeader();
        }

        function maskPhoneInHeader() {
            // Mask phone number in the conversation header
            const phoneElement = document.querySelector("[data-phone]");
            if (phoneElement) {
                const phone = phoneElement.getAttribute("data-phone");
                const text = phoneElement.textContent;
                phoneElement.textContent = text.replace(
                    "Phone " + phone,
                    "Phone " + maskPhoneNumber(phone)
                );
            }
        }
    })();
</script>

<!-- Add Modal for Profile Info -->
<div class="modal fade" id="profileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Robot Memory Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Bootstrap Tabs -->
                <ul class="nav nav-tabs" id="memoryTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="static-memory-tab" data-bs-toggle="tab"
                                data-bs-target="#static-memory" type="button" role="tab" aria-controls="static-memory"
                                aria-selected="true">
                            <i class="bi bi-person-vcard"></i> Static Memory
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="dynamic-memory-tab" data-bs-toggle="tab"
                                data-bs-target="#dynamic-memory" type="button" role="tab" aria-controls="dynamic-memory"
                                aria-selected="false">
                            <i class="bi bi-chat-quote"></i> Dynamic Memory
                        </button>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content mt-3" id="memoryTabsContent">
                    <!-- Static Memory Tab (Profile Information) -->
                    <div class="tab-pane fade show active" id="static-memory" role="tabpanel"
                         aria-labelledby="static-memory-tab">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                <tr>
                                    <th>Key</th>
                                    <th>Value</th>
                                </tr>
                                </thead>
                                <tbody id="profileInfoBody"></tbody>
                            </table>
                        </div>
                        <div id="profileLoading" class="text-center d-none">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Dynamic Memory Tab (Facts) -->
                    <div class="tab-pane fade" id="dynamic-memory" role="tabpanel"
                         aria-labelledby="dynamic-memory-tab">
                        <div id="factsContainer">
                            <div class="list-group" id="factsList"></div>
                        </div>
                        <div id="factsScrollHint" class="text-center text-muted mt-2 d-none">
                            <small><i class="bi bi-arrow-down"></i> Scroll down to see more facts</small>
                        </div>
                        <div id="factsLoading" class="text-center d-none">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading facts...</span>
                            </div>
                        </div>
                        <div id="factsEmpty" class="text-center text-muted d-none">
                            <i class="bi bi-chat-square-dots" style="font-size: 2rem"></i>
                            <p class="mt-2">No facts available for this user</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <button type="button" class="btn btn-warning" id="editProfileBtn" onclick="toggleEditMode()"
                                style="display: none;">
                            <i class="bi bi-pencil"></i> Edit
                        </button>
                        <button type="button" class="btn btn-success" id="updateProfileBtn"
                                onclick="updateProfile()" style="display: none;">
                            <i class="bi bi-check-circle"></i> Update
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script th:inline="javascript">
    const profileModal = new bootstrap.Modal(
        document.getElementById("profileModal")
    );

    let isEditMode = false;
    let originalProfileData = {};

    // Add event listener for when profile modal is hidden
    document.getElementById('profileModal').addEventListener('hidden.bs.modal', function () {
        resetModalState();
    });

    function resetModalState() {
        // Reset all modal state to initial view mode
        const profileInfoBody = document.getElementById("profileInfoBody");
        const factsList = document.getElementById("factsList");
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");

        // Reset variables
        isEditMode = false;
        originalProfileData = {};

        // Reset UI elements
        editBtn.style.display = "none";
        updateBtn.style.display = "none";

        // Clear profile data
        profileInfoBody.innerHTML = "";
        factsList.innerHTML = "";

        // Reset to static memory tab
        const staticMemoryTab = document.getElementById("static-memory-tab");
        const dynamicMemoryTab = document.getElementById("dynamic-memory-tab");
        const staticMemoryContent = document.getElementById("static-memory");
        const dynamicMemoryContent = document.getElementById("dynamic-memory");

        staticMemoryTab.classList.add("active");
        dynamicMemoryTab.classList.remove("active");
        staticMemoryContent.classList.add("show", "active");
        dynamicMemoryContent.classList.remove("show", "active");

        // Hide loading and empty states for facts
        document.getElementById("factsLoading").classList.add("d-none");
        document.getElementById("factsEmpty").classList.add("d-none");
        document.getElementById("factsScrollHint").classList.add("d-none");
    }

    function showProfileModal() {
        const robotId =
            document.querySelector("[data-robot-id]").dataset.robotId;

        const profileInfoBody = document.getElementById("profileInfoBody");
        const profileLoading = document.getElementById("profileLoading");
        const factsList = document.getElementById("factsList");
        const factsLoading = document.getElementById("factsLoading");
        const factsEmpty = document.getElementById("factsEmpty");
        const factsScrollHint = document.getElementById("factsScrollHint");
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");

        // Clear previous content and show loading for both tabs
        profileInfoBody.innerHTML = "";
        factsList.innerHTML = "";
        profileLoading.classList.remove("d-none");
        factsLoading.classList.remove("d-none");
        factsEmpty.classList.add("d-none");
        factsScrollHint.classList.add("d-none");
        editBtn.style.display = "none";
        updateBtn.style.display = "none";
        isEditMode = false;

        // Reset to static memory tab
        const staticMemoryTab = document.getElementById("static-memory-tab");
        const dynamicMemoryTab = document.getElementById("dynamic-memory-tab");
        const staticMemoryContent = document.getElementById("static-memory");
        const dynamicMemoryContent = document.getElementById("dynamic-memory");

        staticMemoryTab.classList.add("active");
        dynamicMemoryTab.classList.remove("active");
        staticMemoryContent.classList.add("show", "active");
        dynamicMemoryContent.classList.remove("show", "active");

        // Fetch profile information (which includes facts)
        fetch(`/robot/api/v1/admin/profile-variables/${robotId}`)
            .then((response) => {
                if (!response.ok) {
                    throw new Error("Failed to fetch memory information");
                }
                return response.json();
            })
            .then((data) => {
                profileLoading.classList.add("d-none");
                factsLoading.classList.add("d-none");

                if (data && data.data) {
                    // Process profile variables (exclude facts)
                    const profileData = {...data.data};
                    delete profileData.facts; // Remove facts from profile data

                    const profileEntries = Object.entries(profileData);
                    if (profileEntries.length > 0) {
                        // Store original data for potential reset (without facts)
                        originalProfileData = {...profileData};

                        profileEntries.forEach((item) => {
                            const row = document.createElement("tr");
                            row.innerHTML = `
                    <td>${item[0]}</td>
                    <td class="profile-value" data-key="${item[0]}">${item[1] || 'null'}</td>
                  `;
                            profileInfoBody.appendChild(row);
                        });

                        // Show edit button when data is loaded
                        editBtn.style.display = "inline-block";
                    } else {
                        profileInfoBody.innerHTML =
                            '<tr><td colspan="2" class="text-center">No profile information available</td></tr>';
                    }

                    // Process facts
                    if (data.data.facts && Array.isArray(data.data.facts) && data.data.facts.length > 0) {
                        const facts = data.data.facts;

                        facts.forEach((fact, index) => {
                            const factItem = document.createElement("div");
                            factItem.className = "list-group-item list-group-item-action";
                            factItem.innerHTML = `
                    <div class="d-flex w-100 justify-content-between">
                      <h6 class="mb-1">Fact #${index + 1}</h6>
                      <small class="text-muted">Dynamic Memory</small>
                    </div>
                    <p class="mb-1">${fact}</p>
                  `;
                            factsList.appendChild(factItem);
                        });

                        // Show scroll hint if there are more than 5 facts
                        if (facts.length > 5) {
                            factsScrollHint.classList.remove("d-none");
                        }
                    } else {
                        factsEmpty.classList.remove("d-none");
                    }
                } else {
                    profileInfoBody.innerHTML =
                        '<tr><td colspan="2" class="text-center">No profile information available</td></tr>';
                    factsEmpty.classList.remove("d-none");
                }
            })
            .catch((error) => {
                profileLoading.classList.add("d-none");
                factsLoading.classList.add("d-none");
                profileInfoBody.innerHTML =
                    '<tr><td colspan="2" class="text-center text-danger">Error loading memory information</td></tr>';
                factsList.innerHTML = `
              <div class="list-group-item list-group-item-danger">
                <h6 class="mb-1">Error</h6>
                <p class="mb-1">Failed to load facts information</p>
              </div>
            `;
                console.error("Error fetching memory information:", error);
            });

        profileModal.show();
    }

    function toggleEditMode() {
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");
        const profileValues = document.querySelectorAll(".profile-value");

        if (!isEditMode) {
            // Enter edit mode
            isEditMode = true;
            editBtn.innerHTML = '<i class="bi bi-x-circle"></i> Cancel';
            editBtn.classList.remove('btn-warning');
            editBtn.classList.add('btn-secondary');
            updateBtn.style.display = "inline-block";

            // Make values editable
            profileValues.forEach(cell => {
                const currentValue = cell.textContent;
                const key = cell.getAttribute('data-key');
                cell.innerHTML = `<input type="text" class="form-control form-control-sm" value="${currentValue}" data-key="${key}">`;
            });
        } else {
            // Cancel edit mode (restore original data)
            cancelEditMode();
        }
    }

    function exitEditMode() {
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");
        const profileValues = document.querySelectorAll(".profile-value");

        // Reset edit mode state
        isEditMode = false;

        // Restore Edit button appearance and hide Update button
        editBtn.innerHTML = '<i class="bi bi-pencil"></i> Edit';
        editBtn.classList.remove('btn-secondary');
        editBtn.classList.add('btn-warning');
        editBtn.style.display = "inline-block";
        updateBtn.style.display = "none";

        // Convert all input fields back to text display
        profileValues.forEach(cell => {
            const input = cell.querySelector('input');
            if (input) {
                const key = input.getAttribute('data-key');
                // Use the current input value (which reflects any edits made)
                const value = input.value;
                // Replace the entire cell content with just the text value
                cell.innerHTML = value;
                // Ensure the data-key attribute is preserved
                cell.setAttribute('data-key', key);
            }
        });
    }

    function cancelEditMode() {
        const editBtn = document.getElementById("editProfileBtn");
        const updateBtn = document.getElementById("updateProfileBtn");
        const profileValues = document.querySelectorAll(".profile-value");

        // Reset edit mode state
        isEditMode = false;

        // Restore Edit button appearance and hide Update button
        editBtn.innerHTML = '<i class="bi bi-pencil"></i> Edit';
        editBtn.classList.remove('btn-secondary');
        editBtn.classList.add('btn-warning');
        editBtn.style.display = "inline-block";
        updateBtn.style.display = "none";

        // Convert all input fields back to text display with original data
        profileValues.forEach(cell => {
            const input = cell.querySelector('input');
            if (input) {
                const key = input.getAttribute('data-key');
                // Use the original data to restore previous state
                const value = originalProfileData[key] || input.value;
                // Replace the entire cell content with just the text value
                cell.innerHTML = value;
                // Ensure the data-key attribute is preserved
                cell.setAttribute('data-key', key);
            }
        });
    }

    function updateProfile() {
        const robotId = document.querySelector("[data-robot-id]").dataset.robotId;
        const profileInputs = document.querySelectorAll(".profile-value input");

        if (!robotId) {
            alert("Robot ID is required");
            return;
        }

        // Collect updated data
        const updatedData = {};
        profileInputs.forEach(input => {
            const key = input.getAttribute('data-key');
            updatedData[key] = input.value;
        });

        // Call API to update profile
        const updateBtn = document.getElementById("updateProfileBtn");
        const originalText = updateBtn.innerHTML;

        // Show loading state
        updateBtn.disabled = true;
        updateBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Updating...';

        fetch(`/robot/api/v1/admin/profile-variables/${robotId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(updatedData)
        })
            .then(response => response.json())
            .then(data => {
                if (data.status === 200) {
                    // Success - update the original data first
                    originalProfileData = {...updatedData};

                    // Exit edit mode and restore view state
                    exitEditMode();

                    // Show success message
                    alert('Profile updated successfully!');
                } else {
                    // Error from server
                    alert('Error updating profile: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error updating profile:', error);
                alert('Error updating profile. Please try again.');
            })
            .finally(() => {
                // Restore button state
                updateBtn.disabled = false;
                updateBtn.innerHTML = originalText;
            });
    }
</script>

<!-- JavaScript to handle loading and navigation -->
<script th:inline="javascript">
    function generateReport() {
        // Show loading overlay
        document.getElementById("loadingOverlay").style.display = "block";

        // Disable the button
        document.getElementById("generateReportBtn").disabled = true;

        // Get the conversation ID
        const conversationId = /*[[${conversation.id}]]*/ 0;

        // Navigate to the report page
        window.location.href = `/web/admin/conversations/${conversationId}/report/view`;

        // No need to hide overlay since we're navigating away
    }
</script>

<script th:inline="javascript">
    function downloadUserMessagesExcel() {
        // Show loading overlay
        document.getElementById("loadingOverlay").style.display = "block";

        // Get the conversation ID
        const conversationId = /*[[${conversation.id}]]*/ 0;

        // Create a form to submit the request
        const form = document.createElement("form");
        form.method = "POST";
        form.action = `/web/admin/conversations/${conversationId}/download-user-messages`;

        // Add CSRF token if needed (uncomment and modify if your application uses CSRF)
        // const csrfToken = document.querySelector('meta[name="_csrf"]').getAttribute('content');
        // const csrfHeader = document.querySelector('meta[name="_csrf_header"]').getAttribute('content');
        // const csrfInput = document.createElement('input');
        // csrfInput.type = 'hidden';
        // csrfInput.name = csrfHeader;
        // csrfInput.value = csrfToken;
        // form.appendChild(csrfInput);

        // Append form to body and submit
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);

        // Hide loading overlay after a short delay
        setTimeout(() => {
            document.getElementById("loadingOverlay").style.display = "none";
        }, 1000);
    }
</script>

<!-- Add Modal for Server Log -->
<div class="modal fade" id="serverLogModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Server Log</h5>
                <div class="ms-auto me-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="copyJsonContent()">
                        <i class="bi bi-clipboard"></i> Copy JSON
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="serverLogContent" class="json-viewer"></div>
            </div>
        </div>
    </div>
</div>

<script src="/js/json-viewer.js"></script>
<script th:inline="javascript">
    const conversationServerLog = /*[[${conversation.serverLog}]]*/ "";
    const serverLogModal = new bootstrap.Modal(
        document.getElementById("serverLogModal")
    );
    const jsonViewer = new JsonViewer(
        document.getElementById("serverLogContent")
    );

    function showServerLogModal() {
        try {
            jsonViewer.setJson(conversationServerLog);
            serverLogModal.show();
        } catch (e) {
            console.error("Error parsing server log:", e);
            document.getElementById("serverLogContent").textContent =
                conversationServerLog;
            serverLogModal.show();
        }
    }

    function copyJsonContent() {
        try {
            const jsonContent =
                typeof conversationServerLog === "string"
                    ? conversationServerLog
                    : JSON.stringify(conversationServerLog, null, 2);

            navigator.clipboard
                .writeText(jsonContent)
                .then(() => {
                    // Show success feedback
                    const copyBtn = document.querySelector(".btn-outline-primary");
                    const originalText = copyBtn.innerHTML;
                    copyBtn.innerHTML = '<i class="bi bi-check2"></i> Copied!';
                    copyBtn.classList.remove("btn-outline-primary");
                    copyBtn.classList.add("btn-success");

                    setTimeout(() => {
                        copyBtn.innerHTML = originalText;
                        copyBtn.classList.remove("btn-success");
                        copyBtn.classList.add("btn-outline-primary");
                    }, 2000);
                })
                .catch((err) => {
                    console.error("Failed to copy: ", err);
                    alert("Failed to copy JSON content");
                });
        } catch (e) {
            console.error("Error copying JSON:", e);
            alert("Error copying JSON content");
        }
    }
</script>

<!-- Add this before the closing body tag -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script th:inline="javascript">
    // Add pronunciation score functions
    function generatePronunciationScore(messageId) {
        console.log("Generating score for message ID:", messageId);
        const button = document.querySelector(
            `button[data-message-id="${messageId}"]`
        );
        if (!button) {
            console.error("Button not found for message ID:", messageId);
            return;
        }

        button.disabled = true;
        button.innerHTML =
            '<i class="bi bi-hourglass-split"></i> Generating...';

        fetch(
            `/web/admin/conversations/messages/${messageId}/pronunciation-score`,
            {
                method: "POST",
            }
        )
            .then((response) => response.json())
            .then((data) => {
                console.log("Pronunciation score data:", data);
                if (data.status === 200) {
                    // Find the message container
                    const messageContainer = button.closest(".message");
                    if (!messageContainer) {
                        console.error("Message container not found");
                        return;
                    }

                    // Create the score container
                    const container = document.createElement("div");
                    container.className = "pronunciation-score-content";
                    container.setAttribute("data-score", JSON.stringify(data.data));

                    // Insert the container before the button
                    button.parentNode.insertBefore(container, button);

                    // Display the score
                    displayPronunciationScore(data.data);

                    // Remove the button
                    button.remove();
                } else {
                    button.disabled = false;
                    button.innerHTML =
                        '<i class="bi bi-mic"></i> Generate Pronunciation Score';
                    alert("Failed to generate pronunciation score");
                }
            })
            .catch((error) => {
                console.error("Error:", error);
                button.disabled = false;
                button.innerHTML =
                    '<i class="bi bi-mic"></i> Generate Pronunciation Score';
                alert("Error generating pronunciation score");
            });
    }

    function displayPronunciationScore(scoreData) {
        console.log("Displaying score data:", scoreData);

        // Find the container that was just created
        const containers = document.querySelectorAll(
            ".pronunciation-score-content"
        );
        let targetContainer = null;

        // Find the container that matches our score data
        containers.forEach((container) => {
            try {
                const containerData = JSON.parse(
                    container.getAttribute("data-score")
                );
                if (JSON.stringify(containerData) === JSON.stringify(scoreData)) {
                    targetContainer = container;
                }
            } catch (e) {
                console.error("Error parsing container data:", e);
            }
        });

        if (!targetContainer) {
            console.error("Score container not found");
            return;
        }

        const textRefs = scoreData.text_refs;
        const result = scoreData.result;
        const totalScore = scoreData.total_score;

        // Determine total score class
        let totalScoreClass = "total-score-green";
        if (totalScore < 0.5) {
            totalScoreClass = "total-score-red";
        } else if (totalScore < 0.7) {
            totalScoreClass = "total-score-yellow";
        }

        let html = `<div class="total-score ${totalScoreClass}">Score: ${(
            totalScore * 100
        ).toFixed(1)}%</div>`;

        result.forEach((word) => {
            // Add all letters in the word
            word.letters.forEach((letter) => {
                const score = letter.score;
                let scoreClass = "score-green";
                if (score < 0.5) {
                    scoreClass = "score-red";
                } else if (score < 0.7) {
                    scoreClass = "score-yellow";
                }

                html += `<span class="letter-score ${scoreClass}" title="Score: ${(
                    score * 100
                ).toFixed(1)}%">${letter.letter}</span>`;
            });
            html += " "; // Add space between words
        });

        // Add regenerate section
        html += `
          <div class="regenerate-section">
            <input type="text" class="regenerate-input" placeholder="Enter text to score..." value="${textRefs}">
            <button class="btn btn-sm btn-outline-primary regenerate-btn" onclick="regenerateScore(this)">
              <i class="bi bi-arrow-clockwise"></i> Re-generate
            </button>
          </div>
        `;

        targetContainer.innerHTML = html;
    }

    function regenerateScore(button) {
        const container = button.closest(".pronunciation-score-content");
        const input = container.querySelector(".regenerate-input");
        const text = input.value.trim();
        const messageId = container.closest(".message").dataset.messageId;

        if (!text) {
            alert("Please enter text to score");
            return;
        }

        button.disabled = true;
        button.innerHTML =
            '<i class="bi bi-hourglass-split"></i> Generating...';

        fetch("/web/admin/conversations/messages/regenerate-score", {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({
                sentenceId: messageId,
                text: text,
            }),
        })
            .then((response) => response.json())
            .then((data) => {
                if (data.status === 200) {
                    container.setAttribute("data-score", JSON.stringify(data.data));
                    displayPronunciationScore(data.data);
                } else {
                    alert("Failed to generate pronunciation score");
                    button.disabled = false;
                    button.innerHTML =
                        '<i class="bi bi-arrow-clockwise"></i> Re-generate';
                }
            })
            .catch((error) => {
                console.error("Error:", error);
                alert("Error generating pronunciation score");
                button.disabled = false;
                button.innerHTML =
                    '<i class="bi bi-arrow-clockwise"></i> Re-generate';
            });
    }

    // Initialize pronunciation scores for existing messages
    document.addEventListener("DOMContentLoaded", function () {
        document
            .querySelectorAll(".pronunciation-score-content")
            .forEach((container) => {
                try {
                    const scoreData = JSON.parse(
                        container.getAttribute("data-score")
                    );
                    console.log("Initializing score for message:", scoreData);
                    displayPronunciationScore(scoreData);
                } catch (error) {
                    console.error("Error initializing pronunciation score:", error);
                }
            });
    });

    // Tăng gain cho audio của user
    function increaseGain(button) {
        const audioId = button.getAttribute('data-audio-id');
        const audioElem = document.getElementById(audioId);
        if (!audioElem) {
            alert('Không tìm thấy audio!');
            return;
        }

        // Lấy gain value
        const gainSelect = document.getElementById('gain-select-' + audioId.split('-')[1]);
        const gainValue = parseFloat(gainSelect ? gainSelect.value : '1');

        // Chỉ tạo context và source một lần
        if (!audioElem._gainContext) {
            try {
                const AudioContext = window.AudioContext || window.webkitAudioContext;
                const context = new AudioContext();
                const source = context.createMediaElementSource(audioElem);
                const gainNode = context.createGain();
                source.connect(gainNode).connect(context.destination);
                audioElem._gainContext = context;
                audioElem._gainNode = gainNode;
                audioElem._gainSource = source;
                console.log('Created new AudioContext for', audioId);
            } catch (error) {
                console.error('CORS error creating MediaElementSource for', audioId, ':', error);

                // Fallback: Thử với proxy hoặc fetch audio
                handleCorsAudioFallback(audioElem, gainValue, audioId);
                return;
            }
        } else {
            // Nếu context bị suspend thì resume
            if (audioElem._gainContext.state === 'suspended') {
                audioElem._gainContext.resume().then(() => {
                    console.log('AudioContext resumed for', audioId);
                });
            }
        }

        try {
            // Set gain
            audioElem._gainNode.gain.value = gainValue;
            console.log('Set gain', gainValue, 'for', audioId);
            // Nếu audio đang dừng thì play lại để áp dụng gain
            if (audioElem.paused) {
                audioElem.play();
            }
        } catch (error) {
            console.error('Error setting gain for', audioId, ':', error);
            showGainErrorMessage(button, 'CORS restriction detected. Audio gain cannot be applied to cross-origin files.');
        }
    }

    // Fallback function để xử lý CORS
    function handleCorsAudioFallback(audioElem, gainValue, audioId) {
        console.log('Attempting CORS fallback for', audioId);

        // Thông báo cho user về hạn chế CORS
        const messageId = audioId.split('-')[1];
        const button = document.querySelector(`[data-audio-id="${audioId}"]`);
        showGainErrorMessage(button, `CORS restriction: Cannot apply gain to cross-origin audio. Try downloading the file first.`);

        // Fallback: Tăng volume của audio element (không hiệu quả bằng Web Audio API)
        try {
            audioElem.volume = Math.min(1.0, audioElem.volume * gainValue);
            console.log('Applied volume fallback:', audioElem.volume, 'for', audioId);
        } catch (e) {
            console.error('Volume fallback also failed:', e);
        }
    }

    // Hiển thị thông báo lỗi gain
    function showGainErrorMessage(button, message) {
        // Tạo tooltip hoặc thông báo tạm thời
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="bi bi-exclamation-triangle"></i> CORS Error';
        button.classList.add('btn-warning');
        button.title = message;

        // Khôi phục sau 3 giây
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-warning');
            button.classList.add('btn-outline-secondary');
            button.title = '';
        }, 3000);

        // Hiển thị console message chi tiết
        console.warn('Audio Gain CORS Issue:', message);
    }
</script>

<!-- ASR QC helpers -->
<script>
    function toggleAsrQc(btn) {
        const form = btn.parentElement.querySelector('.asr-qc-form');
        if (form) form.classList.toggle('d-none');
    }

    function submitAsrQc(btn) {
        const wrapper = btn.closest('.asr-qc-form');
        const messageId = btn.getAttribute('data-message-id');
        if (!wrapper || !messageId) {
            alert('Missing ASR QC context');
            return;
        }

        const inputs = wrapper.querySelectorAll('input');
        const payload = {
            message_id: Number(messageId),
            is_asr_correct: inputs[0]?.checked || false,
            asr_corrected_content: inputs[1]?.value || '',
            is_intent_affected: inputs[2]?.checked || false,
            intent_corrected_content: inputs[3]?.value || ''
        };

        const original = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';

        fetch(`/web/admin/conversations/messages/${messageId}/asr-qc`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(payload)
        })
            .then(r => r.json())
            .then(data => {
                if (data.status === 200) {
                    // Hide the form after save
                    wrapper.classList.add('d-none');
                } else {
                    alert(data.message || 'Update failed');
                }
            })
            .catch(err => {
                console.error('ASR QC update error', err);
                alert('Error updating ASR QC');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = original;
            });
    }

    function submitAsrQcInline(btn) {
        const container = btn.closest('.asr-qc');
        const messageId = btn.getAttribute('data-message-id');
        const payload = {message_id: Number(messageId)};

        container.querySelectorAll('[data-field]').forEach(el => {
            const key = el.getAttribute('data-field');
            // hidden inputs hold boolean or text values
            if (el.type === 'hidden') {
                const val = el.value;
                if (val === 'true' || val === true) payload[key] = true;
                else if (val === 'false' || val === false || val === '') payload[key] = false;
                else payload[key] = val;
            } else {
                payload[key] = el.value || '';
            }
        });

        const original = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';

        fetch(`/web/admin/conversations/messages/${messageId}/asr-qc`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(payload)
        })
            .then(r => r.json())
            .then(data => {
                if (data.status !== 200) {
                    alert(data.message || 'Update failed');
                }
            })
            .catch(err => {
                console.error('ASR QC update error', err);
                alert('Error updating ASR QC');
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = original;
            });
    }

    // Handle boolean icon group (like/dislike) to set hidden value
    function setBooleanIcon(button, value) {
        const group = button.closest('.boolean-icon-group');
        const target = group.getAttribute('data-target');
        const hidden = group.parentElement.querySelector(`input[type="hidden"][data-field="${target}"]`);
        if (hidden) hidden.value = value;

        const buttons = group.querySelectorAll('button');
        buttons.forEach((btn, idx) => {
            const isPositive = idx === 0;
            btn.classList.remove('active');
            if ((value && isPositive) || (!value && !isPositive)) {
                btn.classList.add('active');
            }
        });
    }

    // Toggle a single message ASR QC form (button on each message)
    function toggleAsrQcForm(button) {
        const container = button.closest('.asr-qc');
        const form = container.querySelector('.asr-qc-form');
        if (form) form.classList.toggle('d-none');
    }

    // Expand/Collapse all ASR QC forms from header checkbox
    document.addEventListener('DOMContentLoaded', function () {
        const expandAll = document.getElementById('expandAllAsrQc');
        if (expandAll) {
            expandAll.addEventListener('change', function () {
                const forms = document.querySelectorAll('.asr-qc .asr-qc-form');
                forms.forEach(form => {
                    if (this.checked) {
                        form.classList.remove('d-none');
                    } else {
                        form.classList.add('d-none');
                    }
                });
            });
        }
    });
</script>
</body>

</html>